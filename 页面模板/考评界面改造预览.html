<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考评界面改造预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6 bg-gray-50">
        <div class="max-w-7xl mx-auto space-y-6">
            <!-- 页面标题 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">考评打分</h1>
                    <p class="text-gray-600 mt-2">请根据该员工的实际工作表现客观公正地进行评分</p>
                </div>
            </div>

            <!-- 评价对象信息卡片 -->
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-emerald-500"></div>

                <!-- 主卡片内容 -->
                <div class="relative p-6 h-full">
                    <!-- 右上角装饰 -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                    <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                    <div class="relative z-10">
                        <!-- 头部区域 -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <!-- 评价对象图标 -->
                                <div class="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                    <i data-lucide="user" class="w-6 h-6 text-white"></i>
                                </div>

                                <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                                    评价对象：张三
                                </h3>

                                <!-- 基本信息标签 -->
                                <div class="flex items-center gap-2 mb-4">
                                    <span class="px-3 py-1 rounded-full text-xs font-medium bg-emerald-50 text-emerald-700 border border-emerald-200">
                                        技术部
                                    </span>
                                    <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                                        高级工程师
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- 考评批次信息卡片 -->
                        <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="text-center">
                                    <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-lg font-bold text-slate-900">2024年度绩效考评</div>
                                    <div class="text-xs text-slate-500">考评批次</div>
                                </div>
                                <div class="text-center">
                                    <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="clock" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-sm font-semibold text-slate-900">01-01 ~ 12-31</div>
                                    <div class="text-xs text-slate-500">评分期间</div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

            <!-- 考评说明卡片 -->
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-blue-500"></div>

                <!-- 主卡片内容 -->
                <div class="relative p-6 h-full">
                    <!-- 右上角装饰 -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                    <div class="absolute top-6 right-6 w-8 h-8 bg-blue-50 rounded-full"></div>

                    <div class="relative z-10">
                        <!-- 头部区域 -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <!-- 说明图标 -->
                                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                    <i data-lucide="info" class="w-6 h-6 text-white"></i>
                                </div>

                                <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                    考评说明
                                </h3>

                                <!-- 说明内容 -->
                                <div class="text-sm text-slate-600 space-y-2 leading-relaxed">
                                    <p class="flex items-start">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        请根据该员工的实际工作表现客观公正地进行评分
                                    </p>
                                    <p class="flex items-start">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        您的评分将完全匿名，被评价者无法知晓评分来源
                                    </p>
                                    <p class="flex items-start">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        评分完成后请务必点击"提交评分"按钮保存
                                    </p>
                                    <p class="flex items-start">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        如有疑问，请联系HR部门或系统管理员
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-blue-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

            <!-- 评分项列表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 数值评分示例 -->
                <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
                    <!-- 顶部状态指示条 -->
                    <div class="absolute top-0 left-0 right-0 h-1 bg-blue-500"></div>

                    <!-- 主卡片内容 -->
                    <div class="relative p-6 h-full">
                        <!-- 右上角装饰 -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                        <div class="absolute top-6 right-6 w-8 h-8 bg-blue-50 rounded-full"></div>

                        <div class="relative z-10">
                            <!-- 头部区域 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <!-- 评分项图标 -->
                                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                        <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                                    </div>

                                    <h4 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                        工作质量 <span class="text-red-500 ml-1">*</span>
                                    </h4>

                                    <!-- 评分模式和描述 -->
                                    <div class="flex items-center gap-2 mb-4">
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                            数值评分
                                        </span>
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200">
                                            必填项
                                        </span>
                                    </div>

                                    <!-- 描述区域 -->
                                    <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">评估员工完成工作任务的质量水平，包括准确性、完整性和专业性</p>
                                </div>
                            </div>
                            
                            <!-- 评分内容区域 -->
                            <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                                <div class="text-center mb-4">
                                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="calculator" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-sm text-slate-500 mb-2">当前评分</div>
                                    <div class="flex items-center justify-center space-x-2 mb-4">
                                        <input type="number" min="0" max="10" step="0.1" value="8.5"
                                               class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-center text-lg font-bold">
                                        <span class="text-sm text-gray-500">/ 10 分</span>
                                    </div>
                                </div>
                                
                                <!-- 评分滑块 -->
                                <div class="mb-4">
                                    <input type="range" min="0" max="10" step="0.1" value="8.5"
                                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>0</span>
                                        <span>10</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 底部装饰线 -->
                            <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                        </div>

                        <!-- 悬停时的微妙阴影效果 -->
                        <div class="absolute inset-0 bg-blue-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                </div>

                <!-- 等级评分示例 -->
                <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
                    <!-- 顶部状态指示条 -->
                    <div class="absolute top-0 left-0 right-0 h-1 bg-green-500"></div>

                    <!-- 主卡片内容 -->
                    <div class="relative p-6 h-full">
                        <!-- 右上角装饰 -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                        <div class="absolute top-6 right-6 w-8 h-8 bg-green-50 rounded-full"></div>

                        <div class="relative z-10">
                            <!-- 头部区域 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <!-- 评分项图标 -->
                                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                        <i data-lucide="star" class="w-6 h-6 text-white"></i>
                                    </div>

                                    <h4 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-green-600 transition-colors duration-300">
                                        团队协作
                                    </h4>

                                    <!-- 评分模式和描述 -->
                                    <div class="flex items-center gap-2 mb-4">
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                                            等级评分
                                        </span>
                                    </div>

                                    <!-- 描述区域 -->
                                    <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">评估员工在团队中的协作能力和沟通效果</p>
                                </div>
                            </div>
                            
                            <!-- 评分内容区域 -->
                            <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                                <div class="text-center mb-4">
                                    <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="star" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-sm text-slate-500">选择等级</div>
                                </div>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                    <label class="relative flex cursor-pointer rounded-lg border border-green-500 bg-green-50 p-3 hover:bg-gray-50 focus:outline-none transition-all duration-200">
                                        <input type="radio" name="teamwork" value="excellent" checked class="sr-only peer">
                                        <div class="flex flex-1 flex-col">
                                            <div class="flex items-center justify-between">
                                                <span class="block text-sm font-medium text-gray-900">优秀</span>
                                                <span class="ml-2 text-sm font-semibold text-green-600">9分</span>
                                            </div>
                                            <span class="mt-1 block text-xs text-gray-500">团队协作能力突出</span>
                                        </div>
                                    </label>
                                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50 focus:outline-none transition-all duration-200">
                                        <input type="radio" name="teamwork" value="good" class="sr-only peer">
                                        <div class="flex flex-1 flex-col">
                                            <div class="flex items-center justify-between">
                                                <span class="block text-sm font-medium text-gray-900">良好</span>
                                                <span class="ml-2 text-sm font-semibold text-green-600">7分</span>
                                            </div>
                                            <span class="mt-1 block text-xs text-gray-500">团队协作能力较好</span>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- 底部装饰线 -->
                            <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                        </div>

                        <!-- 悬停时的微妙阴影效果 -->
                        <div class="absolute inset-0 bg-green-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>
