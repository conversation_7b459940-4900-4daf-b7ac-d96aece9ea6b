<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考评模板管理改造预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            <!-- 页面标题 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">考评模板管理</h1>
                    <p class="text-gray-600 mt-2">创建和管理考评表单模板，设置评分标准</p>
                </div>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span>新建模板</span>
                </button>
            </div>

            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">模板总数</p>
                            <p class="text-2xl font-bold text-gray-900">6</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">启用模板</p>
                            <p class="text-2xl font-bold text-gray-900">4</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">默认模板</p>
                            <p class="text-2xl font-bold text-gray-900">1</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <i data-lucide="layers" class="w-6 h-6 text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">模板类型</p>
                            <p class="text-2xl font-bold text-gray-900">3</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div class="flex-1 max-w-lg">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                                </div>
                                <input type="text" placeholder="搜索模板名称或描述..."
                                       class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <select class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">所有类型</option>
                                <option value="structured">结构化评分</option>
                                <option value="open">开放式评分</option>
                                <option value="mixed">混合式评分</option>
                            </select>
                            <select class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">所有状态</option>
                                <option value="active">已启用</option>
                                <option value="inactive">已禁用</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模板卡片网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 启用的模板示例 -->
                <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
                    <!-- 顶部状态指示条 -->
                    <div class="absolute top-0 left-0 right-0 h-1 bg-emerald-500"></div>

                    <!-- 主卡片内容 -->
                    <div class="relative p-6 h-full">
                        <!-- 右上角装饰 -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                        <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                        <div class="relative z-10">
                            <!-- 头部区域 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <!-- 模板图标 -->
                                    <div class="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                        <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                                    </div>

                                    <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                                        年度绩效考评模板
                                    </h3>

                                    <!-- 状态和类型标签 -->
                                    <div class="flex items-center gap-2 mb-4">
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-emerald-50 text-emerald-700 border border-emerald-200">
                                            <i data-lucide="check-circle" class="w-3 h-3 mr-1 inline"></i>
                                            启用
                                        </span>
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                                            结构化评分
                                        </span>
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200">
                                            <i data-lucide="star" class="w-3 h-3 mr-1 inline"></i>
                                            默认
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- 描述区域 -->
                            <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">用于年度员工绩效考核的标准模板，包含工作质量、团队协作等多个维度</p>

                            <!-- 统计信息卡片 -->
                            <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                                <div class="grid grid-cols-3 gap-4">
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="list" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-slate-900">8</div>
                                        <div class="text-xs text-slate-500">评分项</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-slate-900">100</div>
                                        <div class="text-xs text-slate-500">总分</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-slate-900">01-15</div>
                                        <div class="text-xs text-slate-500">创建时间</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="flex items-center gap-2">
                                <button class="flex-1 bg-emerald-50 border border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                    详情
                                </button>
                                <button class="flex-1 bg-slate-50 border border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                    编辑
                                </button>
                                <button class="bg-blue-50 border border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm" title="复制模板">
                                    <i data-lucide="copy" class="w-4 h-4"></i>
                                </button>
                                <button class="bg-rose-50 border border-rose-200 text-rose-600 hover:bg-rose-100 hover:border-rose-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm" title="删除模板">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>

                <!-- 禁用的模板示例 -->
                <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
                    <!-- 顶部状态指示条 -->
                    <div class="absolute top-0 left-0 right-0 h-1 bg-slate-400"></div>

                    <!-- 主卡片内容 -->
                    <div class="relative p-6 h-full">
                        <!-- 右上角装饰 -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                        <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                        <div class="relative z-10">
                            <!-- 头部区域 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <!-- 模板图标 -->
                                    <div class="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                        <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                                    </div>

                                    <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                                        新员工试用期考评
                                    </h3>

                                    <!-- 状态和类型标签 -->
                                    <div class="flex items-center gap-2 mb-4">
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-50 text-slate-600 border border-slate-200">
                                            <i data-lucide="pause-circle" class="w-3 h-3 mr-1 inline"></i>
                                            禁用
                                        </span>
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                                            混合式评分
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- 描述区域 -->
                            <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">新员工试用期综合能力评估模板，包含基础技能和适应性评价</p>

                            <!-- 统计信息卡片 -->
                            <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                                <div class="grid grid-cols-3 gap-4">
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="list" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-slate-900">5</div>
                                        <div class="text-xs text-slate-500">评分项</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-slate-900">80</div>
                                        <div class="text-xs text-slate-500">总分</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-slate-900">03-05</div>
                                        <div class="text-xs text-slate-500">创建时间</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="flex items-center gap-2">
                                <button class="flex-1 bg-emerald-50 border border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                    详情
                                </button>
                                <button class="flex-1 bg-slate-50 border border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                    编辑
                                </button>
                                <button class="bg-blue-50 border border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm" title="复制模板">
                                    <i data-lucide="copy" class="w-4 h-4"></i>
                                </button>
                                <button class="bg-rose-50 border border-rose-200 text-rose-600 hover:bg-rose-100 hover:border-rose-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm" title="删除模板">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>
