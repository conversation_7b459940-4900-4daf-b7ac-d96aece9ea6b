<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考评模板卡片展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            <!-- 页面标题 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">考评模板管理</h1>
                    <p class="text-gray-600 mt-2">管理系统中的所有考评模板和评分项配置</p>
                </div>
            </div>

            <!-- 卡片视图 -->
            <div id="cardView" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 卡片内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const templates = [
            {
                id: 1,
                name: "年度绩效考评模板",
                description: "用于年度员工绩效考核的标准模板",
                category: "绩效考核",
                status: "启用",
                itemCount: 8,
                createTime: "2024-01-15",
                updateTime: "2024-07-20",
            },
            {
                id: 2,
                name: "季度项目评估模板",
                description: "季度项目完成情况评估模板",
                category: "项目评估",
                status: "启用",
                itemCount: 6,
                createTime: "2024-02-10",
                updateTime: "2024-06-15",
            },
            {
                id: 3,
                name: "新员工试用期考评",
                description: "新员工试用期综合能力评估",
                category: "试用期考核",
                status: "停用",
                itemCount: 5,
                createTime: "2024-03-05",
                updateTime: "2024-05-20",
            },
            {
                id: 4,
                name: "部门主管评估模板",
                description: "针对部门主管的综合管理能力评估",
                category: "管理评估",
                status: "启用",
                itemCount: 10,
                createTime: "2024-04-12",
                updateTime: "2024-08-01",
            },
            {
                id: 5,
                name: "技术专员考核模板",
                description: "专门用于技术岗位员工的专业技能考核",
                category: "技能考核",
                status: "启用",
                itemCount: 7,
                createTime: "2024-05-20",
                updateTime: "2024-07-15",
            },
            {
                id: 6,
                name: "销售业绩评估模板",
                description: "销售团队业绩和客户服务质量评估",
                category: "业绩考核",
                status: "停用",
                itemCount: 6,
                createTime: "2024-06-08",
                updateTime: "2024-06-25",
            }
        ];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            renderCardView();
        });

        // 渲染卡片视图
        function renderCardView() {
            const cardView = document.getElementById('cardView');
            cardView.innerHTML = '';

            templates.forEach(template => {
                const card = document.createElement('div');
                card.className = 'group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200';
                card.innerHTML = `
                    <!-- 顶部状态指示条 -->
                    <div class="absolute top-0 left-0 right-0 h-1 ${template.status === '启用' ? 'bg-emerald-500' : 'bg-slate-400'}"></div>

                    <!-- 主卡片内容 -->
                    <div class="relative p-6 h-full">
                        <!-- 右上角装饰 -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                        <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                        <div class="relative z-10">
                            <!-- 头部区域 -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <!-- 模板图标 -->
                                    <div class="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                        <i data-lucide="settings" class="w-6 h-6 text-white"></i>
                                    </div>

                                    <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                                        ${template.name}
                                    </h3>

                                    <!-- 状态和分类标签 -->
                                    <div class="flex items-center gap-2 mb-4">
                                        <span class="px-3 py-1 rounded-full text-xs font-medium ${template.status === '启用' ? 'bg-emerald-50 text-emerald-700 border border-emerald-200' : 'bg-slate-50 text-slate-600 border border-slate-200'}">
                                            ${template.status}
                                        </span>
                                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                                            ${template.category}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- 描述区域 -->
                            <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">${template.description}</p>

                            <!-- 统计信息卡片 -->
                            <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="settings" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-2xl font-bold text-slate-900">${template.itemCount}</div>
                                        <div class="text-xs text-slate-500">评分项</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                            <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-slate-900">${template.updateTime.slice(5)}</div>
                                        <div class="text-xs text-slate-500">最近更新</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="flex items-center gap-2">
                                <button class="flex-1 bg-emerald-50 border border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                    详情
                                </button>
                                <button class="flex-1 bg-slate-50 border border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                    编辑
                                </button>
                                <button class="bg-rose-50 border border-rose-200 text-rose-600 hover:bg-rose-100 hover:border-rose-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                `;
                cardView.appendChild(card);
            });
            lucide.createIcons();
        }
    </script>
</body>
</html>
