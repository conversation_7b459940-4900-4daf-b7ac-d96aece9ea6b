ERROR 2025-07-28 16:20:41,800 jwt_auth 24084 39344 ����JWT tokenʧ��: The following fields do not exist in this model, are m2m fields, primary keys, or are non-concrete fields: last_token_refresh
ERROR 2025-07-28 16:20:44,781 jwt_auth 24084 39344 ����JWT tokenʧ��: The following fields do not exist in this model, are m2m fields, primary keys, or are non-concrete fields: last_token_refresh
ERROR 2025-07-28 16:20:50,705 jwt_auth 24084 39344 ����JWT tokenʧ��: The following fields do not exist in this model, are m2m fields, primary keys, or are non-concrete fields: last_token_refresh
ERROR 2025-07-28 16:22:35,988 jwt_auth 34444 6880 ����JWT tokenʧ��: The following fields do not exist in this model, are m2m fields, primary keys, or are non-concrete fields: last_token_refresh
ERROR 2025-07-28 16:22:36,609 jwt_auth 34444 6880 ����JWT tokenʧ��: The following fields do not exist in this model, are m2m fields, primary keys, or are non-concrete fields: last_token_refresh
WARNING 2025-07-28 16:38:49,524 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:38:49,591 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:38:52,987 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:38:53,037 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:01,868 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:01,957 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:02,777 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:02,820 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:03,568 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:03,622 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:04,164 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:04,209 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:05,790 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:05,887 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:11,290 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:11,342 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:12,715 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:12,774 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-28 16:39:24,657 jwt_auth 6724 19364 Token�ѱ�����: 35e4462a-ee12-439f-a651-0ecece616abf
WARNING 2025-07-29 08:56:44,945 jwt_auth 37532 5392 Token�ѱ�����: 65a5618a-7c95-4185-a743-f0135303ef9d
ERROR 2025-07-29 08:56:44,945 jwt_auth 37532 5392 ˢ��tokenʧ��: Invalid refresh token
WARNING 2025-07-29 08:57:12,551 jwt_auth 37532 20368 Token�ѱ�����: 33bc3c38-971d-49f5-94b4-d207eb1e93ee
ERROR 2025-07-29 08:57:12,552 jwt_auth 37532 20368 ˢ��tokenʧ��: Invalid refresh token
WARNING 2025-07-29 09:24:02,889 jwt_auth 10016 31184 Token�ѱ�����: b53785a7-a2ec-4b4d-b95d-195f6d309edc
WARNING 2025-07-29 09:24:03,466 jwt_auth 10016 28172 Token�ѱ�����: b002218a-1af2-4c08-8b87-ace343789e96
WARNING 2025-07-29 10:12:06,157 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,159 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,161 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,162 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,164 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,169 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,171 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,173 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,175 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,176 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,182 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,184 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,185 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,187 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,188 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,192 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,193 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,195 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,196 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,197 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,200 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,201 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,202 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,203 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
WARNING 2025-07-29 10:12:06,205 anonymous 10492 9544 Ǩ�������־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value', 'created_by_id'
ERROR 2025-07-29 12:36:30,630 permissions 6656 23560 ��ɫ�����־��¼ʧ��: AuditLog() got unexpected keyword arguments: 'table_name', 'record_id', 'field_name', 'old_value', 'new_value'
WARNING 2025-07-29 12:45:45,101 jwt_auth 32044 22900 Token�ѱ�����: bb9623e1-e5d6-4912-bd62-5bce75104b7c
WARNING 2025-07-29 13:28:45,022 jwt_auth 30460 25920 Token�ѱ�����: 0b5450eb-49dd-4b91-8997-f0f752200474
WARNING 2025-07-29 13:43:17,122 jwt_auth 27032 27160 Token�ѱ�����: 4344906c-4a70-4581-8ea7-928ab1631d83
WARNING 2025-07-29 13:50:22,984 jwt_auth 12148 35052 Token�ѱ�����: 18c3d4d2-104e-4631-85b6-023e9cc0c9cc
WARNING 2025-07-29 13:53:16,281 jwt_auth 37940 16048 Token�ѱ�����: b0ff8ceb-dead-4ffe-bd12-79a1968576b9
WARNING 2025-07-29 13:53:17,535 jwt_auth 37940 16048 Token�ѱ�����: b0ff8ceb-dead-4ffe-bd12-79a1968576b9
WARNING 2025-07-29 13:56:49,178 jwt_auth 38044 31900 Token�ѱ�����: 94451a72-82df-4dc6-beea-8466dccdc0f0
WARNING 2025-07-29 13:58:36,371 jwt_auth 38044 6892 Token�ѱ�����: adc07eaf-aba4-41a5-a1f5-d1685aa5b7ec
WARNING 2025-07-29 14:13:05,931 jwt_auth 6456 31224 Token�ѱ�����: 5f35941e-14bf-4fbb-bea0-fa38e0f2a420
WARNING 2025-07-29 14:28:48,325 jwt_auth 8008 31436 Token�ѱ�����: a4973630-c7f8-4dd6-b071-1a5eac350532
WARNING 2025-07-29 14:28:51,204 jwt_auth 8008 31436 Token�ѱ�����: a4973630-c7f8-4dd6-b071-1a5eac350532
WARNING 2025-07-29 14:30:12,574 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:30:23,931 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:30:29,503 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:31:21,470 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:31:24,757 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:31:25,961 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:31:27,920 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:31:30,217 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:31:31,748 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:00,233 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:01,542 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:03,265 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:05,054 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:06,123 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:07,422 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:41,710 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:32:45,959 jwt_auth 25972 17676 Token�ѱ�����: 1953cf51-3b40-4c53-bdfc-7b61c54d65ce
WARNING 2025-07-29 14:40:39,067 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:41,462 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:42,296 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:44,082 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:45,319 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:47,587 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:49,272 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:50,229 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:51,268 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:52,385 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:53,236 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:54,026 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:55,169 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:40:56,789 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:05,207 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:07,368 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:12,179 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:14,131 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:15,862 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:18,770 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:21,871 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:23,797 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:33,142 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:39,596 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:45,918 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:51,117 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:53,626 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:55,262 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:56,799 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:57,887 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:58,901 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:41:59,600 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:42:00,117 jwt_auth 35892 35148 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:51:53,096 jwt_auth 6620 30060 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:51:59,639 jwt_auth 6620 30060 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:04,838 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:08,541 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:10,730 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:12,331 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:12,662 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:21,510 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:57:23,652 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 14:58:23,726 jwt_auth 6620 31848 Token�ѱ�����: 2a6ece9f-cdf7-45c4-9310-7beeafc71c69
WARNING 2025-07-29 15:00:00,790 jwt_auth 10624 38552 Token�ѱ�����: 2ba2c380-817a-4989-bd16-2045f3b2f92b
WARNING 2025-07-29 15:04:09,617 jwt_auth 10624 38552 Token�ѱ�����: 2ba2c380-817a-4989-bd16-2045f3b2f92b
WARNING 2025-07-29 15:04:14,721 jwt_auth 10624 38552 Token�ѱ�����: 2ba2c380-817a-4989-bd16-2045f3b2f92b
WARNING 2025-07-29 15:04:19,704 jwt_auth 10624 38552 Token�ѱ�����: 2ba2c380-817a-4989-bd16-2045f3b2f92b
WARNING 2025-07-29 15:24:36,870 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:40,969 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:42,421 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:43,977 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:45,485 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:47,271 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:48,958 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:24:51,148 jwt_auth 16864 33556 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:25:03,211 jwt_auth 16864 5296 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:32:39,189 jwt_auth 16864 29336 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:32:41,923 jwt_auth 16864 29336 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:32:44,132 jwt_auth 16864 29336 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:32:45,905 jwt_auth 16864 29336 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:32:47,391 jwt_auth 16864 29336 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:35:58,731 jwt_auth 25792 2180 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:36:38,603 jwt_auth 25792 2180 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
WARNING 2025-07-29 15:36:41,546 jwt_auth 25792 2180 Token�ѱ�����: 19bca8a9-b542-4d47-95fe-bedb9c211c28
ERROR 2025-07-30 09:29:49,667 exceptions 37096 24364 ϵͳ�쳣: Unknown field(s) (scoring_mode, max_score) specified for EvaluationTemplate
ERROR 2025-07-30 09:29:52,026 exceptions 37096 24364 ϵͳ�쳣: Unknown field(s) (scoring_mode, max_score) specified for EvaluationTemplate
ERROR 2025-07-30 09:29:53,535 exceptions 37096 24364 ϵͳ�쳣: Unknown field(s) (scoring_mode, max_score) specified for EvaluationTemplate
ERROR 2025-07-30 11:21:46,461 exceptions 26792 2948 ϵͳ�쳣: admin/position/detail.html
ERROR 2025-07-30 11:36:36,980 exceptions 13136 21852 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:36:42,477 exceptions 13136 21852 ϵͳ�쳣: admin/position/update.html
ERROR 2025-07-30 11:38:16,770 exceptions 17012 22148 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:38:22,016 exceptions 17012 22148 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:38:28,025 exceptions 17012 22148 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:38:34,008 exceptions 17012 22148 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:41:17,218 exceptions 17012 22148 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:46:17,759 exceptions 17012 11964 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:46:22,814 exceptions 17012 11964 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:46:50,084 exceptions 17012 11964 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:47:08,399 exceptions 17012 11964 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:47:31,752 exceptions 36952 14700 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 11:57:44,639 exceptions 36952 16352 ϵͳ�쳣: default requires 2 arguments, 1 provided
ERROR 2025-07-30 12:08:22,399 exceptions 36952 22352 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:08:27,629 exceptions 36952 22352 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:08:46,904 exceptions 36952 22352 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:08:48,555 exceptions 36952 22352 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:08:50,080 exceptions 36952 22352 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:09:05,282 exceptions 34492 35324 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:09:11,012 exceptions 34492 35324 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:09:17,018 exceptions 34492 35324 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:09:40,195 exceptions 34492 35324 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:09:47,013 exceptions 34492 35324 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:09:53,010 exceptions 34492 35324 ϵͳ�쳣: Invalid block tag on line 12: 'static'. Did you forget to register or load this tag?
ERROR 2025-07-30 12:16:17,382 exceptions 3996 20116 ϵͳ�쳣: Unknown field(s) (scoring_mode, max_score) specified for EvaluationTemplate
ERROR 2025-07-30 12:16:21,578 exceptions 3996 8912 ϵͳ�쳣: Unknown field(s) (parent) specified for Department
ERROR 2025-07-30 12:16:48,146 exceptions 3996 26720 ϵͳ�쳣: Unknown field(s) (parent) specified for Department
ERROR 2025-07-30 12:17:00,343 exceptions 3996 18976 ϵͳ�쳣: Unknown field(s) (scoring_mode, max_score) specified for EvaluationTemplate
ERROR 2025-07-30 12:18:03,614 exceptions 28992 24168 ϵͳ�쳣: Unknown field(s) (parent) specified for Department
ERROR 2025-07-30 12:18:09,004 exceptions 28992 24168 ϵͳ�쳣: Unknown field(s) (parent) specified for Department
ERROR 2025-07-30 12:18:15,006 exceptions 28992 24168 ϵͳ�쳣: Unknown field(s) (parent) specified for Department
ERROR 2025-07-30 12:18:21,014 exceptions 28992 24168 ϵͳ�쳣: Unknown field(s) (parent) specified for Department
ERROR 2025-07-30 12:21:14,948 exceptions 11416 28276 ϵͳ�쳣: admin/position/update.html
ERROR 2025-07-30 12:29:57,991 exceptions 37384 13716 ϵͳ�쳣: admin/position/detail.html
ERROR 2025-07-30 12:34:30,857 exceptions 27428 17420 ϵͳ�쳣: admin/position/update.html
ERROR 2025-07-30 13:37:31,145 exceptions 3300 23224 ϵͳ�쳣: 'url_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
i18n
l10n
log
static
tz
ERROR 2025-07-30 13:37:40,928 exceptions 3300 23224 ϵͳ�쳣: admin/template/detail.html
ERROR 2025-07-30 13:37:43,785 exceptions 3300 23224 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-30 14:22:32,962 exceptions 2692 25468 ϵͳ�쳣: organizations/position_confirm_delete.html
ERROR 2025-07-30 14:36:14,922 exceptions 4576 13996 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-30 15:47:33,301 exceptions 35040 21884 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-30 15:47:37,424 exceptions 35040 21884 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-30 15:48:44,563 exceptions 35040 5624 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-30 15:48:49,034 exceptions 35040 5624 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-30 16:53:33,165 exceptions 3280 37072 ϵͳ�쳣: admin/permissions/role_details.html
ERROR 2025-07-30 17:20:04,683 exceptions 39912 22396 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-30 17:20:09,779 exceptions 39912 22396 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-30 17:29:27,891 exceptions 31664 7272 ϵͳ�쳣: admin/permissions/role_details.html
ERROR 2025-07-30 17:29:51,929 exceptions 31664 7272 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-30 17:29:54,422 exceptions 31664 7272 ϵͳ�쳣: admin/template/detail.html
ERROR 2025-07-30 17:30:15,917 exceptions 31664 7272 ϵͳ�쳣: admin/template/detail.html
ERROR 2025-07-30 17:34:32,540 exceptions 31664 7272 ϵͳ�쳣: admin/batch/detail.html
ERROR 2025-07-30 17:34:43,000 exceptions 31664 7272 ϵͳ�쳣: admin/batch/update.html
ERROR 2025-07-30 17:34:45,551 exceptions 31664 7272 ϵͳ�쳣: admin/batch/detail.html
ERROR 2025-07-30 17:34:49,374 exceptions 31664 7272 ϵͳ�쳣: admin/batch/assign.html
ERROR 2025-07-30 17:35:20,973 exceptions 31664 7272 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-31 11:12:59,600 exceptions 31388 5900 ϵͳ�쳣: Invalid block tag on line 42: 'endinclude', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 11:13:02,539 exceptions 31388 5900 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-31 11:14:18,077 exceptions 31388 5900 ϵͳ�쳣: organizations/position_confirm_delete.html
ERROR 2025-07-31 11:27:28,006 exceptions 5184 31664 ϵͳ�쳣: organizations/department_confirm_delete.html
ERROR 2025-07-31 12:25:20,623 exceptions 36916 37392 ϵͳ�쳣: Invalid block tag on line 42: 'endinclude', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:25:23,184 exceptions 36916 37392 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-31 12:28:27,855 exceptions 36220 41428 ϵͳ�쳣: Invalid block tag on line 42: 'endinclude', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:36:30,555 exceptions 40224 29508 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-31 12:36:34,648 exceptions 40224 29508 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-31 12:37:21,078 exceptions 41724 31140 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-31 12:37:53,514 exceptions 41724 31140 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-31 12:40:03,714 exceptions 41724 31140 ϵͳ�쳣: Invalid block tag on line 243: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:40:09,965 exceptions 41724 31140 ϵͳ�쳣: Invalid block tag on line 243: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:40:15,045 exceptions 41724 31140 ϵͳ�쳣: Invalid block tag on line 243: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:40:20,096 exceptions 41724 31140 ϵͳ�쳣: Invalid block tag on line 243: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:40:25,148 exceptions 41724 31140 ϵͳ�쳣: Invalid block tag on line 243: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 12:48:40,854 exceptions 29164 27376 ϵͳ�쳣: 'admin' is not a registered namespace inside 'communications'
ERROR 2025-07-31 12:48:43,620 exceptions 29164 27376 ϵͳ�쳣: Cannot resolve keyword 'priority' into field. Choices are: announcement_type, author, author_id, content, created_at, created_by, deleted_at, deleted_by, expire_at, id, is_html, is_pinned, is_published, publish_at, target_department, target_department_id, title, updated_at, updated_by, view_count
ERROR 2025-07-31 12:50:12,537 exceptions 29164 27376 ϵͳ�쳣: admin/permissions/role_details.html
ERROR 2025-07-31 12:52:44,197 exceptions 29484 14088 ϵͳ�쳣: admin/permissions/role_details.html
ERROR 2025-07-31 12:53:17,952 exceptions 29484 14088 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:20,691 exceptions 29484 14088 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:26,305 exceptions 29484 14088 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:32,310 exceptions 29484 14088 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:38,650 exceptions 4328 32708 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:42,256 exceptions 4328 32708 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:47,312 exceptions 4328 32708 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:53,306 exceptions 4328 32708 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 12:53:59,313 exceptions 4328 32708 ϵͳ�쳣: Reverse for 'message_center' not found. 'message_center' is not a valid view function or pattern name.
ERROR 2025-07-31 13:07:58,812 exceptions 2952 10552 ϵͳ�쳣: Reverse for 'talent_list' not found. 'talent_list' is not a valid view function or pattern name.
ERROR 2025-07-31 13:35:06,231 exceptions 19500 31648 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-31 13:35:09,838 exceptions 19500 31648 ϵͳ�쳣: admin/template/detail.html
ERROR 2025-07-31 13:35:14,282 exceptions 19500 31648 ϵͳ�쳣: admin/template/detail.html
ERROR 2025-07-31 13:36:48,805 exceptions 19500 31648 ϵͳ�쳣: admin/batch/detail.html
ERROR 2025-07-31 13:43:06,374 exceptions 19500 32560 ϵͳ�쳣: organizations/department_confirm_delete.html
ERROR 2025-07-31 13:43:45,981 exceptions 19500 32560 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-31 13:43:57,193 exceptions 19500 32560 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-31 14:14:02,390 exceptions 2448 17512 ϵͳ�쳣: Reverse for 'staff_batch_status' not found. 'staff_batch_status' is not a valid view function or pattern name.
ERROR 2025-07-31 14:14:09,320 exceptions 2448 17512 ϵͳ�쳣: Reverse for 'staff_batch_status' not found. 'staff_batch_status' is not a valid view function or pattern name.
ERROR 2025-07-31 14:16:18,550 exceptions 2448 31948 ϵͳ�쳣: Reverse for 'staff_batch_status' not found. 'staff_batch_status' is not a valid view function or pattern name.
ERROR 2025-07-31 14:16:22,898 exceptions 2448 31948 ϵͳ�쳣: Reverse for 'staff_batch_status' not found. 'staff_batch_status' is not a valid view function or pattern name.
ERROR 2025-07-31 14:34:05,260 exceptions 25328 31372 ϵͳ�쳣: admin/batch/detail.html
ERROR 2025-07-31 14:34:07,887 exceptions 25328 31372 ϵͳ�쳣: admin/batch/update.html
ERROR 2025-07-31 15:11:29,634 exceptions 24980 19672 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-31 15:20:04,053 exceptions 35380 600 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-31 15:20:09,852 exceptions 35380 600 ϵͳ�쳣: admin/template/update.html
ERROR 2025-07-31 15:34:31,969 exceptions 21648 35692 ϵͳ�쳣: admin/batch/detail.html
ERROR 2025-07-31 15:43:48,306 exceptions 19940 25496 ϵͳ�쳣: admin/batch/detail.html
ERROR 2025-07-31 15:44:34,593 exceptions 19940 25496 ϵͳ�쳣: Cannot resolve keyword 'year' into field. Choices are: allow_self_evaluation, anonymous_results, auto_assign, batchtemplate, created_at, created_by, default_template, default_template_id, deleted_at, deleted_by, description, end_date, evaluationprogress, evaluationrelation, evaluationreport, id, is_active, name, start_date, status, talentassessment, talentmatrix, templates, updated_at, updated_by
ERROR 2025-07-31 15:44:37,749 exceptions 19940 20056 ϵͳ�쳣: Cannot resolve keyword 'year' into field. Choices are: allow_self_evaluation, anonymous_results, auto_assign, batchtemplate, created_at, created_by, default_template, default_template_id, deleted_at, deleted_by, description, end_date, evaluationprogress, evaluationrelation, evaluationreport, id, is_active, name, start_date, status, talentassessment, talentmatrix, templates, updated_at, updated_by
ERROR 2025-07-31 15:44:43,305 exceptions 19940 20056 ϵͳ�쳣: Cannot resolve keyword 'year' into field. Choices are: allow_self_evaluation, anonymous_results, auto_assign, batchtemplate, created_at, created_by, default_template, default_template_id, deleted_at, deleted_by, description, end_date, evaluationprogress, evaluationrelation, evaluationreport, id, is_active, name, start_date, status, talentassessment, talentmatrix, templates, updated_at, updated_by
ERROR 2025-07-31 15:44:49,323 exceptions 19940 20056 ϵͳ�쳣: Cannot resolve keyword 'year' into field. Choices are: allow_self_evaluation, anonymous_results, auto_assign, batchtemplate, created_at, created_by, default_template, default_template_id, deleted_at, deleted_by, description, end_date, evaluationprogress, evaluationrelation, evaluationreport, id, is_active, name, start_date, status, talentassessment, talentmatrix, templates, updated_at, updated_by
ERROR 2025-07-31 15:44:55,307 exceptions 19940 20056 ϵͳ�쳣: Cannot resolve keyword 'year' into field. Choices are: allow_self_evaluation, anonymous_results, auto_assign, batchtemplate, created_at, created_by, default_template, default_template_id, deleted_at, deleted_by, description, end_date, evaluationprogress, evaluationrelation, evaluationreport, id, is_active, name, start_date, status, talentassessment, talentmatrix, templates, updated_at, updated_by
ERROR 2025-07-31 15:51:12,044 exceptions 28940 4804 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-07-31 15:58:08,027 exceptions 36388 36544 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-07-31 15:58:12,122 exceptions 36388 36544 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-07-31 16:29:21,544 exceptions 19352 18600 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-07-31 16:29:25,177 exceptions 19352 18600 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-07-31 16:45:50,087 exceptions 4708 29832 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:47:04,851 exceptions 20624 31428 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:47:10,314 exceptions 20624 31428 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:47:16,316 exceptions 20624 31428 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:47:22,327 exceptions 20624 31428 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:47:33,333 exceptions 32456 2308 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:47:56,759 exceptions 20000 664 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:02,316 exceptions 20000 664 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:08,330 exceptions 20000 664 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:13,627 exceptions 15972 36080 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:19,299 exceptions 15972 36080 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:39,425 exceptions 15972 10212 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:45,307 exceptions 15972 10212 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:51,306 exceptions 15972 10212 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:48:57,308 exceptions 15972 10212 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:51:51,746 exceptions 15972 16024 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:51:57,308 exceptions 15972 16024 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:52:03,321 exceptions 15972 16024 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:52:09,306 exceptions 15972 16024 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:52:21,399 exceptions 15972 22948 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:52:27,309 exceptions 15972 22948 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:52:33,316 exceptions 15972 22948 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:52:39,300 exceptions 15972 22948 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:03,862 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:09,307 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:15,305 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:21,312 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:24,283 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:29,356 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:35,306 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-07-31 16:53:41,313 exceptions 15972 7228 ϵͳ�쳣: Invalid block tag on line 269: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-08-01 11:58:13,583 exceptions 14980 7896 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-08-01 12:21:21,883 exceptions 32692 9836 ϵͳ�쳣: admin/anonymous/code_history.html
ERROR 2025-08-01 12:28:17,145 exceptions 36076 5368 ϵͳ�쳣: admin/anonymous/code_history.html
ERROR 2025-08-01 12:29:29,473 exceptions 36076 5368 ϵͳ�쳣: admin/template/update.html
ERROR 2025-08-01 12:29:54,802 exceptions 36076 5368 ϵͳ�쳣: admin/template/update.html
ERROR 2025-08-01 12:30:31,749 exceptions 36076 5368 ϵͳ�쳣: admin/template/update.html
ERROR 2025-08-01 12:32:37,777 exceptions 32720 5472 ϵͳ�쳣: admin/template/update.html
ERROR 2025-08-01 12:32:46,511 exceptions 32720 5472 ϵͳ�쳣: admin/template/update.html
ERROR 2025-08-01 12:33:06,351 exceptions 32204 9836 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 12:33:52,065 exceptions 23444 9424 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 12:42:03,764 exceptions 32032 20604 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-08-01 12:46:10,576 exceptions 32032 31064 ϵͳ�쳣: admin/batch/update.html
ERROR 2025-08-01 12:49:38,281 exceptions 22772 23524 ϵͳ�쳣: add_message() argument must be an HttpRequest object, not 'BatchActivateView'.
ERROR 2025-08-01 12:53:46,385 exceptions 23168 36076 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 13:02:08,415 exceptions 18300 23164 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 13:02:16,224 exceptions 18300 23164 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 13:02:54,283 exceptions 30944 23460 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 13:03:27,432 exceptions 22632 6992 ϵͳ�쳣: admin/batch/update.html
ERROR 2025-08-01 13:04:14,309 exceptions 8340 10312 ϵͳ�쳣: Tried to update field evaluations.EvaluationTemplate.updated_by with a model instance, <Staff: EMP001 - ���Թ���Ա>. Use a value compatible with CharField.
ERROR 2025-08-01 13:11:43,760 exceptions 35340 21024 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 13:11:48,779 exceptions 35340 21024 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 13:11:58,854 exceptions 35340 21024 ϵͳ�쳣: admin/batch/update.html
ERROR 2025-08-01 13:50:48,696 exceptions 4956 30804 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:50:48,710 exceptions 4956 30804 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:50:48,731 exceptions 4956 5220 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:54:01,969 exceptions 4956 5220 ϵͳ�쳣: No URL to redirect to.  Either provide a url or define a get_absolute_url method on the Model.
ERROR 2025-08-01 13:54:41,167 exceptions 4956 5220 ϵͳ�쳣: No URL to redirect to.  Either provide a url or define a get_absolute_url method on the Model.
ERROR 2025-08-01 13:54:46,311 exceptions 4956 5220 ϵͳ�쳣: No URL to redirect to.  Either provide a url or define a get_absolute_url method on the Model.
ERROR 2025-08-01 13:54:52,350 exceptions 4956 5220 ϵͳ�쳣: No URL to redirect to.  Either provide a url or define a get_absolute_url method on the Model.
ERROR 2025-08-01 13:54:58,338 exceptions 4956 5220 ϵͳ�쳣: No URL to redirect to.  Either provide a url or define a get_absolute_url method on the Model.
ERROR 2025-08-01 13:56:37,727 exceptions 7348 22820 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:56:37,739 exceptions 7348 22820 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:56:37,751 exceptions 7348 22820 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:56:40,265 exceptions 7348 22820 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:56:40,279 exceptions 7348 22820 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:56:40,289 exceptions 7348 22820 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 13:57:22,817 exceptions 12320 23176 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-08-01 14:08:38,645 exceptions 38572 37244 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 14:08:38,657 exceptions 38572 37244 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 14:08:38,673 exceptions 38572 37244 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 14:09:05,522 exceptions 31200 38280 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-08-01 14:26:06,256 exceptions 4188 40516 ϵͳ�쳣: 'EvaluationTemplate' object has no attribute 'evaluationbatch_set'
ERROR 2025-08-01 14:29:45,003 exceptions 39424 40584 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-08-01 15:37:08,296 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:37:08,311 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:37:08,323 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:38:41,734 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:38:41,748 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:38:41,758 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:39:05,251 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:39:05,266 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:39:05,282 exceptions 41004 41428 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:40:06,299 exceptions 8196 24964 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:40:06,316 exceptions 8196 24964 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:40:06,330 exceptions 8196 24964 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:42:09,105 exceptions 8196 24964 ϵͳ�쳣: type object 'EvaluationBatch' has no attribute 'STATUS_CHOICES'
ERROR 2025-08-01 15:50:51,808 exceptions 8196 36072 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:50:51,821 exceptions 8196 36072 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:50:51,836 exceptions 8196 36072 ϵͳ�쳣: �෽��װ������Ҫrequest����
ERROR 2025-08-01 15:52:44,463 exceptions 8196 36072 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 15:53:08,246 exceptions 8196 36072 ϵͳ�쳣: Invalid filter: 'sub'
ERROR 2025-08-01 15:53:31,181 exceptions 8196 36072 ϵͳ�쳣: Generic detail view ProgressDetailView must be called with either an object pk or a slug in the URLconf.
