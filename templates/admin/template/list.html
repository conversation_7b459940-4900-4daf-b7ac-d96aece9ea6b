{% extends "admin/base_admin.html" %}

{% block page_title %}考评模板管理{% endblock %}
{% block page_description %}创建和管理考评表单模板，设置评分标准{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:template_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建模板</span>
</a>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .template-card-enhanced {
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        background: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        transform: translateY(0);
    }

    .template-card-enhanced:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
    }

    .card-decoration {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 4rem;
        height: 4rem;
        background: #f1f5f9;
        border-radius: 50%;
        opacity: 0.6;
    }

    .card-decoration-small {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        width: 2rem;
        height: 2rem;
        background: #ecfdf5;
        border-radius: 50%;
    }
</style>
{% endblock %}

{% block admin_content %}
{% csrf_token %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">模板总数</p>
                <p class="text-2xl font-bold text-gray-900">{{ templates|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">启用模板</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_templates|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">默认模板</p>
                <p class="text-2xl font-bold text-gray-900">1</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="layers" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">模板类型</p>
                <p class="text-2xl font-bold text-gray-900">3</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="搜索模板名称或描述...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有类型</option>
                    <option value="structured">结构化评分</option>
                    <option value="open">开放式评分</option>
                    <option value="mixed">混合式评分</option>
                </select>
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">已启用</option>
                    <option value="inactive">已禁用</option>
                </select>
                <div class="flex items-center">
                    <input type="checkbox" id="showDisabledFilter" class="checkbox mr-2" 
                           onchange="toggleShowDisabled()" 
                           {% if show_disabled == 'true' %}checked{% endif %}>
                    <label for="showDisabledFilter" class="text-sm text-gray-700 cursor-pointer">
                        显示已禁用
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="templatesGrid">
    {% for template in templates %}
        <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200 template-card"
             data-type="{{ template.template_type }}"
             data-status="{% if template.is_active %}active{% else %}inactive{% endif %}">

            <!-- 顶部状态指示条 -->
            <div class="absolute top-0 left-0 right-0 h-1 {% if template.is_active %}bg-emerald-500{% else %}bg-slate-400{% endif %}"></div>

            <!-- 主卡片内容 -->
            <div class="relative p-6 h-full">
                <!-- 右上角装饰 -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                <div class="relative z-10">
                    <!-- 头部区域 -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <!-- 模板图标 -->
                            <div class="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                            </div>

                            <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                                {{ template.name }}
                            </h3>

                            <!-- 状态和类型标签 -->
                            <div class="flex items-center gap-2 mb-4">
                                {% if template.is_active %}
                                    <span class="px-3 py-1 rounded-full text-xs font-medium bg-emerald-50 text-emerald-700 border border-emerald-200">
                                        <i data-lucide="check-circle" class="w-3 h-3 mr-1 inline"></i>
                                        启用
                                    </span>
                                {% else %}
                                    <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-50 text-slate-600 border border-slate-200">
                                        <i data-lucide="pause-circle" class="w-3 h-3 mr-1 inline"></i>
                                        禁用
                                    </span>
                                {% endif %}

                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                                    {% if template.template_type == 'structured' %}结构化评分
                                    {% elif template.template_type == 'open' %}开放式评分
                                    {% elif template.template_type == 'mixed' %}混合式评分
                                    {% endif %}
                                </span>

                                {% if template.is_default %}
                                    <span class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200">
                                        <i data-lucide="star" class="w-3 h-3 mr-1 inline"></i>
                                        默认
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 描述区域 -->
                    <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">{{ template.description|default:"暂无描述" }}</p>

                    <!-- 统计信息卡片 -->
                    <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="list" class="w-4 h-4 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-slate-900">{{ template.get_items_count }}</div>
                                <div class="text-xs text-slate-500">评分项</div>
                            </div>
                            <div class="text-center">
                                <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-slate-900">{{ template.calculate_total_score }}</div>
                                <div class="text-xs text-slate-500">总分</div>
                            </div>
                            <div class="text-center">
                                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                                </div>
                                <div class="text-sm font-semibold text-slate-900">{{ template.created_at|date:"m-d" }}</div>
                                <div class="text-xs text-slate-500">创建时间</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="flex items-center gap-2">
                        <a href="{% url 'evaluations:admin:template_detail' template.pk %}"
                           class="flex-1 bg-emerald-50 border border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                            详情
                        </a>
                        <a href="{% url 'evaluations:admin:template_update' template.pk %}"
                           class="flex-1 bg-slate-50 border border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm flex items-center justify-center gap-1">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                            编辑
                        </a>
                        <button onclick="copyTemplate({{ template.pk }}, '{{ template.name }}')"
                                class="bg-blue-50 border border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm" title="复制模板">
                            <i data-lucide="copy" class="w-4 h-4"></i>
                        </button>
                        <button onclick="deleteTemplate({{ template.pk }}, '{{ template.name }}')"
                                class="bg-rose-50 border border-rose-200 text-rose-600 hover:bg-rose-100 hover:border-rose-300 transition-all duration-300 px-3 py-2 rounded-lg text-sm" title="删除模板">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <!-- 底部装饰线 -->
                <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
            </div>

            <!-- 悬停时的微妙阴影效果 -->
            <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        </div>
    {% empty %}
        <!-- Empty State -->
        <div class="col-span-3">
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md border border-slate-200 p-12 text-center">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-blue-500"></div>

                <!-- 右上角装饰 -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                <div class="absolute top-6 right-6 w-8 h-8 bg-blue-50 rounded-full"></div>

                <div class="relative z-10">
                    <!-- 空状态图标 -->
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="file-plus" class="w-8 h-8 text-blue-600"></i>
                    </div>

                    <h3 class="text-xl font-bold text-slate-900 mb-3">暂无考评模板</h3>
                    <p class="text-slate-600 text-sm leading-relaxed mb-6">开始创建第一个考评模板，设置评分标准和规则。</p>

                    <a href="{% url 'evaluations:admin:template_create' %}"
                       class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-sm">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        创建模板
                    </a>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTemplates();
    });

    // 类型筛选
    document.getElementById('typeFilter').addEventListener('change', function() {
        filterTemplates();
    });

    // 状态筛选
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterTemplates();
    });

    function filterTemplates() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const templateCards = document.querySelectorAll('.template-card');

        templateCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();
            const type = card.dataset.type;
            const status = card.dataset.status;

            let showCard = true;

            // 文本搜索
            if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) {
                showCard = false;
            }

            // 类型筛选
            if (typeFilter && type !== typeFilter) {
                showCard = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
        });
    }

    // 复制模板
    function copyTemplate(templateId, templateName) {
        if (confirm(`确定要复制模板"${templateName}"吗？`)) {
            fetch(`/evaluations/admin/templates/${templateId}/copy/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('模板复制成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('复制失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('复制失败，请重试', 'error');
            });
        }
    }

    // 删除模板
    function deleteTemplate(templateId, templateName) {
        if (confirm(`确定要删除模板"${templateName}"吗？此操作不可恢复。`)) {
            fetch(`/evaluations/admin/templates/${templateId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('模板删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('删除失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('删除失败，请重试', 'error');
            });
        }
    }

    // 显示/隐藏已禁用模板
    function toggleShowDisabled() {
        const checkbox = document.getElementById('showDisabledFilter');
        const showDisabled = checkbox.checked;
        
        const url = new URL(window.location);
        if (showDisabled) {
            url.searchParams.set('show_disabled', 'true');
        } else {
            url.searchParams.delete('show_disabled');
        }
        
        window.location.href = url.toString();
    }

    // 搜索框自动聚焦
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>
{% endblock %}