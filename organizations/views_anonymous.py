# -*- coding: utf-8 -*-
"""
匿名编号管理视图
实现匿名编号系统的管理界面

功能包括：
- 匿名编号状态监控
- 批量迁移和生成
- 安全编号管理
- 迁移历史查看
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q, Count, Case, When, IntegerField
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.views.generic import ListView
from django.utils import timezone
from datetime import datetime, timedelta
import json
import logging

from .models import Staff, Department
from common.security.permissions import (
    Permission, require_permission,
    permission_manager
)
from common.security.anonymous import (
    SecureAnonymousCodeGenerator,
    AnonymousCodeValidator,
    AnonymousCodeMigrator
)

logger = logging.getLogger(__name__)


class AnonymousCodesManageView(ListView):
    """匿名编号管理主页面"""
    model = Staff
    template_name = 'admin/anonymous/manage.html'
    context_object_name = 'staff_codes'
    paginate_by = 20
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """获取员工查询集"""
        queryset = Staff.objects.filter(deleted_at__isnull=True).select_related('department')
        
        # 搜索功能
        search = self.request.GET.get('search', '').strip()
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(employee_no__icontains=search) |
                Q(anonymous_code__icontains=search) |
                Q(new_anonymous_code__icontains=search)
            )
        
        # 状态筛选
        status_filter = self.request.GET.get('status', '').strip()
        if status_filter == 'new':
            queryset = queryset.filter(new_anonymous_code__isnull=False)
        elif status_filter == 'old':
            queryset = queryset.filter(new_anonymous_code__isnull=True)
        
        return queryset.order_by('department__name', 'employee_no')
    
    def get_context_data(self, **kwargs):
        """获取模板上下文数据"""
        context = super().get_context_data(**kwargs)
        
        # 统计数据
        context['stats'] = self._get_security_stats()
        context['total_staff_codes'] = Staff.objects.filter(deleted_at__isnull=True).count()
        
        # 搜索参数
        context['search'] = self.request.GET.get('search', '')
        context['status_filter'] = self.request.GET.get('status', '')
        
        return context
    
    def _get_security_stats(self):
        """获取安全统计数据"""
        # 基础统计
        total_staff = Staff.objects.filter(deleted_at__isnull=True).count()
        new_codes_count = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=False
        ).count()
        old_codes_count = total_staff - new_codes_count
        
        # 计算百分比
        new_codes_percentage = int((new_codes_count / total_staff * 100)) if total_staff > 0 else 0
        
        # 最后迁移时间
        last_migration = Staff.objects.filter(
            anonymous_code_generated_at__isnull=False
        ).order_by('-anonymous_code_generated_at').first()
        
        return {
            'total_staff': total_staff,
            'new_codes_count': new_codes_count,
            'old_codes_count': old_codes_count,
            'new_codes_percentage': new_codes_percentage,
            'last_migration': last_migration.anonymous_code_generated_at if last_migration else None
        }


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def migrate_anonymous_codes_batch(request):
    """批量迁移匿名编号"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})
    
    try:
        data = json.loads(request.body)
        dry_run = data.get('dry_run', False)
        backward_compatible = data.get('backward_compatible', True)
        
        migrator = AnonymousCodeMigrator()
        
        # 获取需要迁移的员工
        staff_to_migrate = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=True
        )
        
        if dry_run:
            # 预演模式
            return JsonResponse({
                'success': True,
                'migrated_count': staff_to_migrate.count(),
                'message': f'预演成功，将迁移 {staff_to_migrate.count()} 个员工的匿名编号'
            })
        
        # 实际迁移
        migrated_count = 0
        failed_count = 0
        
        with transaction.atomic():
            for staff in staff_to_migrate:
                try:
                    success, result = migrator.migrate_single_staff(staff)
                    if success:
                        migrated_count += 1
                    else:
                        failed_count += 1
                        logger.warning(f"迁移员工 {staff.name} 失败: {result}")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"迁移员工 {staff.name} 时出错: {str(e)}")
        
        return JsonResponse({
            'success': True,
            'migrated_count': migrated_count,
            'failed_count': failed_count,
            'message': f'迁移完成，成功: {migrated_count}，失败: {failed_count}'
        })
        
    except Exception as e:
        logger.error(f"批量迁移失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '迁移失败，请稍后重试'})


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def generate_single_anonymous_code(request):
    """生成单个匿名编号"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})
    
    try:
        data = json.loads(request.body)
        staff_id = data.get('staff_id')
        
        if not staff_id:
            return JsonResponse({'success': False, 'message': '缺少员工ID'})
        
        staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
        
        generator = SecureAnonymousCodeGenerator()
        
        with transaction.atomic():
            # 生成新的安全编号
            department_id = staff.department.id if staff.department else 1
            new_code = generator.generate_secure_code(staff.id, department_id)

            # 更新员工记录
            staff.new_anonymous_code = new_code
            staff.anonymous_code_generated_at = datetime.now()
            staff.anonymous_code_version = 'v2.0'
            staff.save(update_fields=[
                'new_anonymous_code',
                'anonymous_code_generated_at',
                'anonymous_code_version'
            ])
        
        return JsonResponse({
            'success': True,
            'new_code': new_code,
            'message': f'为 {staff.name} 生成新匿名编号成功'
        })
        
    except Exception as e:
        logger.error(f"生成单个匿名编号失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '生成失败，请稍后重试'})


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def generate_batch_anonymous_codes(request):
    """批量生成匿名编号"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})
    
    try:
        data = json.loads(request.body)
        staff_ids = data.get('staff_ids', [])
        
        if not staff_ids:
            return JsonResponse({'success': False, 'message': '未选择任何员工'})
        
        if not isinstance(staff_ids, list):
            return JsonResponse({'success': False, 'message': '员工ID格式错误'})
        
        # 获取有效的员工
        valid_staff = Staff.objects.filter(
            id__in=staff_ids,
            deleted_at__isnull=True,
            is_active=True
        )
        
        if not valid_staff.exists():
            return JsonResponse({'success': False, 'message': '未找到有效的员工'})
        
        generator = SecureAnonymousCodeGenerator()
        success_count = 0
        failed_count = 0
        results = []
        
        with transaction.atomic():
            for staff in valid_staff:
                try:
                    # 生成新的安全编号
                    department_id = staff.department.id if staff.department else 1
                    new_code = generator.generate_secure_code(staff.id, department_id)
                    
                    # 更新员工记录
                    staff.new_anonymous_code = new_code
                    staff.anonymous_code_generated_at = datetime.now()
                    staff.anonymous_code_version = 'v2.0'
                    staff.save(update_fields=[
                        'new_anonymous_code',
                        'anonymous_code_generated_at',
                        'anonymous_code_version'
                    ])
                    
                    success_count += 1
                    results.append({
                        'staff_id': staff.id,
                        'staff_name': staff.name,
                        'new_code': new_code,
                        'success': True
                    })
                    
                    logger.info(f"批量生成匿名编号成功: {staff.name} -> {new_code}")
                    
                except Exception as e:
                    failed_count += 1
                    results.append({
                        'staff_id': staff.id,
                        'staff_name': staff.name,
                        'success': False,
                        'error': str(e)
                    })
                    logger.error(f"批量生成匿名编号失败: {staff.name} - {str(e)}")
        
        return JsonResponse({
            'success': True,
            'message': f'批量生成完成：成功 {success_count} 个，失败 {failed_count} 个',
            'success_count': success_count,
            'failed_count': failed_count,
            'results': results
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': '请求数据格式错误'})
    except Exception as e:
        logger.error(f"批量生成匿名编号失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '批量生成失败，请稍后重试'})


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def anonymous_code_history(request, staff_id):
    """查看匿名编号历史"""
    staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
    
    from common.models import AuditLog
    
    # 获取相关审计日志
    logs = AuditLog.objects.filter(
        target_model='Staff',
        target_id=staff_id,
        description__icontains='匿名编号'
    ).order_by('-created_at')[:20]
    
    # 编号变更历史
    code_history = []
    
    # 当前编号信息
    current_info = {
        'type': '当前编号',
        'old_code': staff.anonymous_code,
        'new_code': staff.new_anonymous_code,
        'generated_at': staff.anonymous_code_generated_at,
        'version': staff.anonymous_code_version,
        'is_current': True
    }
    code_history.append(current_info)
    
    # 从日志中提取历史信息
    for log in logs:
        if log.extra_data:
            extra_data = log.extra_data
            if 'old_anonymous_code' in extra_data or 'new_anonymous_code' in extra_data:
                history_item = {
                    'type': '历史变更',
                    'old_code': extra_data.get('old_anonymous_code'),
                    'new_code': extra_data.get('new_anonymous_code'),
                    'generated_at': log.created_at,
                    'version': extra_data.get('version', 'v1.0'),
                    'operator': log.user,
                    'is_current': False
                }
                code_history.append(history_item)
    
    context = {
        'staff': staff,
        'code_history': code_history,
        'audit_logs': logs
    }
    
    return render(request, 'admin/anonymous/code_history.html', context)


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def check_anonymous_codes_status(request):
    """检查匿名编号状态（API）"""
    try:
        # 统计信息
        stats = Staff.objects.filter(deleted_at__isnull=True).aggregate(
            total_count=Count('id'),
            with_new_code=Count('id', filter=Q(new_anonymous_code__isnull=False)),
            with_old_code_only=Count('id', filter=Q(new_anonymous_code__isnull=True))
        )
        
        # 部门统计
        department_stats = Department.objects.filter(
            deleted_at__isnull=True
        ).annotate(
            total_staff=Count('staff', filter=Q(staff__deleted_at__isnull=True)),
            upgraded_staff=Count('staff', filter=Q(
                staff__deleted_at__isnull=True,
                staff__new_anonymous_code__isnull=False
            ))
        ).values('name', 'total_staff', 'upgraded_staff')
        
        # 最近生成的编号
        recent_codes = Staff.objects.filter(
            anonymous_code_generated_at__isnull=False
        ).order_by('-anonymous_code_generated_at')[:10].values(
            'name', 'employee_no', 'new_anonymous_code', 'anonymous_code_generated_at'
        )
        
        return JsonResponse({
            'success': True,
            'stats': stats,
            'department_stats': list(department_stats),
            'recent_codes': list(recent_codes)
        })
        
    except Exception as e:
        logger.error(f"检查匿名编号状态失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '检查失败'})


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def validate_anonymous_code(request):
    """验证匿名编号格式（API）"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})
    
    try:
        data = json.loads(request.body)
        code = data.get('code', '').strip().upper()
        
        if not code:
            return JsonResponse({'success': False, 'message': '编号不能为空'})
        
        validator = AnonymousCodeValidator()
        is_valid, error_message, staff = validator.validate_login_code(code)
        
        result = {
            'success': True,
            'is_valid': is_valid,
            'message': error_message or '验证成功'
        }
        
        if staff:
            result['staff_info'] = {
                'name': staff.name,
                'employee_no': staff.employee_no,
                'department': staff.department.name if staff.department else '未分配'
            }
        
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"验证匿名编号失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '验证失败'})


# ==================== 匿名编号删除功能 ====================

class AnonymousCodeDeleteView(View):
    """匿名编号删除视图"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def post(self, request, staff_id):
        """删除单个员工的匿名编号"""
        try:
            data = json.loads(request.body)
            reason = data.get('reason', '').strip()
            force_delete = data.get('force_delete', False)
            
            if not reason:
                return JsonResponse({
                    'success': False,
                    'message': '请输入删除原因'
                })
            
            staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
            
            # 检查是否有匿名编号可删除
            if not staff.new_anonymous_code:
                return JsonResponse({
                    'success': False,
                    'message': '该员工没有新安全匿名编号，无需删除'
                })
            
            # 检查数据完整性
            integrity_check = self._check_data_integrity(staff)
            if integrity_check['has_conflicts'] and not force_delete:
                return JsonResponse({
                    'success': False,
                    'has_conflicts': True,
                    'conflicts': integrity_check['conflicts'],
                    'message': '该员工的匿名编号存在相关数据，请确认是否强制删除'
                })
            
            # 执行删除
            with transaction.atomic():
                old_code = staff.new_anonymous_code
                staff.new_anonymous_code = None
                staff.anonymous_code_generated_at = None
                staff.anonymous_code_version = 'v1.0'  # 回退到旧版
                staff.save(update_fields=[
                    'new_anonymous_code', 
                    'anonymous_code_generated_at', 
                    'anonymous_code_version'
                ])
                
                # 记录审计日志
                from common.models import AuditLog
                AuditLog.objects.create(
                    user=request.staff.username,
                    action='DELETE',
                    target_model='Staff',
                    target_id=staff.id,
                    description=f'删除匿名编号: {staff.name} ({staff.employee_no}), 旧编号: {old_code}, 原因: {reason}',
                    ip_address=request.META.get('REMOTE_ADDR', '127.0.0.1'),
                    extra_data={
                        'deleted_anonymous_code': old_code,
                        'reason': reason,
                        'force_delete': force_delete
                    }
                )
            
            return JsonResponse({
                'success': True,
                'message': f'成功删除员工 {staff.name} 的匿名编号'
            })
            
        except Exception as e:
            logger.error(f"删除匿名编号失败: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': '删除失败，请稍后重试'
            })
    
    def _check_data_integrity(self, staff):
        """检查匿名编号的数据完整性"""
        conflicts = []
        
        try:
            # 检查是否有使用新匿名编号的登录记录
            from .models import StaffLoginLog
            login_records = StaffLoginLog.objects.filter(
                staff=staff,
                login_type='anonymous',
                created_at__gte=staff.anonymous_code_generated_at if staff.anonymous_code_generated_at else timezone.now() - timedelta(days=30)
            ).count()
            if login_records > 0:
                conflicts.append(f'存在 {login_records} 条使用新匿名编号的登录记录')
        except Exception as e:
            logger.warning(f"检查登录记录时出错: {e}")
        
        # 检查是否有考评记录
        try:
            from evaluations.models import EvaluationRecord
            eval_records = EvaluationRecord.objects.filter(
                Q(relation__evaluator=staff) | Q(relation__evaluatee=staff),
                deleted_at__isnull=True,
                created_at__gte=staff.anonymous_code_generated_at if staff.anonymous_code_generated_at else timezone.now() - timedelta(days=30)
            ).count()
            if eval_records > 0:
                conflicts.append(f'存在 {eval_records} 条使用新匿名编号的考评记录')
        except ImportError:
            pass  # 如果没有考评模块则跳过
        
        return {
            'has_conflicts': len(conflicts) > 0,
            'conflicts': conflicts
        }


class AnonymousCodeBatchDeleteView(View):
    """匿名编号批量删除视图"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def post(self, request):
        """批量删除员工匿名编号"""
        try:
            data = json.loads(request.body)
            staff_ids = data.get('staff_ids', [])
            reason = data.get('reason', '').strip()
            force_delete = data.get('force_delete', False)
            
            if not staff_ids or not reason:
                return JsonResponse({
                    'success': False,
                    'message': '参数不完整'
                })
            
            # 获取要删除匿名编号的员工列表
            staff_list = Staff.objects.filter(
                id__in=staff_ids,
                deleted_at__isnull=True,
                new_anonymous_code__isnull=False  # 只处理有新匿名编号的员工
            ).select_related('department')
            
            if not staff_list.exists():
                return JsonResponse({
                    'success': False,
                    'message': '没有找到有新匿名编号的员工'
                })
            
            success_count = 0
            failed_items = []
            conflict_items = []
            
            for staff in staff_list:
                try:
                    # 检查数据完整性
                    integrity_check = AnonymousCodeDeleteView()._check_data_integrity(staff)
                    if integrity_check['has_conflicts'] and not force_delete:
                        conflict_items.append({
                            'name': staff.name,
                            'employee_no': staff.employee_no,
                            'department': staff.department.name if staff.department else '未分配',
                            'anonymous_code': staff.new_anonymous_code,
                            'conflicts': integrity_check['conflicts']
                        })
                        continue
                    
                    # 执行删除
                    with transaction.atomic():
                        old_code = staff.new_anonymous_code
                        staff.new_anonymous_code = None
                        staff.anonymous_code_generated_at = None
                        staff.anonymous_code_version = 'v1.0'  # 回退到旧版
                        staff.save(update_fields=[
                            'new_anonymous_code', 
                            'anonymous_code_generated_at', 
                            'anonymous_code_version'
                        ])
                        
                        # 记录审计日志
                        from common.models import AuditLog
                        AuditLog.objects.create(
                            user=request.staff.username,
                            action='BATCH_DELETE',
                            target_model='Staff',
                            target_id=staff.id,
                            description=f'批量删除匿名编号: {staff.name} ({staff.employee_no}), 旧编号: {old_code}, 原因: {reason}',
                            ip_address=request.META.get('REMOTE_ADDR', '127.0.0.1'),
                            extra_data={
                                'deleted_anonymous_code': old_code,
                                'reason': reason,
                                'force_delete': force_delete
                            }
                        )
                    
                    success_count += 1
                    
                except Exception as e:
                    failed_items.append({
                        'name': staff.name,
                        'reason': str(e)
                    })
            
            # 如果有冲突但未强制删除，返回冲突信息
            if conflict_items and not force_delete:
                return JsonResponse({
                    'success': False,
                    'has_conflicts': True,
                    'conflict_items': conflict_items[:10],  # 只返回前10个
                    'success_count': success_count,
                    'failed_count': len(failed_items),
                    'conflict_count': len(conflict_items),
                    'message': f'有 {len(conflict_items)} 个员工的匿名编号存在关联数据，需要确认强制删除'
                })
            
            return JsonResponse({
                'success': True,
                'success_count': success_count,
                'failed_count': len(failed_items),
                'conflict_count': len(conflict_items),
                'failed_items': failed_items[:10],  # 只返回前10个失败项
                'message': f'批量删除完成：成功 {success_count} 个，失败 {len(failed_items)} 个'
            })
            
        except Exception as e:
            logger.error(f"批量删除匿名编号失败: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': '批量删除失败，请稍后重试'
            })


# ==================== 辅助视图 ====================

@require_permission(Permission.SYS_MANAGE_SETTINGS)
def anonymous_codes_export(request):
    """导出匿名编号列表"""
    import openpyxl
    from django.http import HttpResponse
    from io import BytesIO
    
    try:
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "匿名编号列表"
        
        # 表头
        headers = [
            '员工姓名', '员工编号', '部门', '旧匿名编号', 
            '新安全编号', '生成时间', '版本', '状态'
        ]
        ws.append(headers)
        
        # 数据
        staff_list = Staff.objects.filter(deleted_at__isnull=True).select_related('department').order_by(
            'department__name', 'employee_no'
        )
        
        for staff in staff_list:
            row = [
                staff.name,
                staff.employee_no,
                staff.department.name if staff.department else '未分配',
                staff.anonymous_code,
                staff.new_anonymous_code or '未生成',
                staff.anonymous_code_generated_at.strftime('%Y-%m-%d %H:%M:%S') if staff.anonymous_code_generated_at else '',
                staff.anonymous_code_version,
                '已升级' if staff.new_anonymous_code else '待升级'
            ]
            ws.append(row)
        
        # 保存到内存
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        # 创建响应
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="anonymous_codes_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
        
        return response
        
    except Exception as e:
        logger.error(f"导出匿名编号列表失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '导出失败'})


@require_permission(Permission.SYS_MANAGE_SETTINGS)
def anonymous_system_settings(request):
    """匿名编号系统设置页面"""
    if request.method == 'GET':
        # 获取当前设置
        from common.models import SystemSettings
        
        settings_keys = [
            'anonymous_code_backward_compatible_days',
            'anonymous_code_batch_size',
            'anonymous_code_algorithm_version',
            'anonymous_code_format_pattern'
        ]
        
        current_settings = {}
        for key in settings_keys:
            try:
                setting = SystemSettings.objects.get(key=key, is_active=True)
                current_settings[key] = setting.get_typed_value()
            except SystemSettings.DoesNotExist:
                # 默认值
                defaults = {
                    'anonymous_code_backward_compatible_days': 30,
                    'anonymous_code_batch_size': 100,
                    'anonymous_code_algorithm_version': 'v2.0',
                    'anonymous_code_format_pattern': 'XXXX-XXXX-XXXX'
                }
                current_settings[key] = defaults.get(key)
        
        context = {
            'settings': current_settings,
            'algorithm_versions': ['v1.0', 'v2.0'],
            'format_patterns': ['XXXX-XXXX-XXXX', 'XXXXXXXXXXXXXXXX']
        }
        
        return render(request, 'admin/anonymous/settings.html', context)
    
    elif request.method == 'POST':
        # 更新设置
        try:
            data = json.loads(request.body)
            
            from common.models import SystemSettings
            
            for key, value in data.items():
                if key.startswith('anonymous_code_'):
                    SystemSettings.objects.update_or_create(
                        key=key,
                        defaults={
                            'value': str(value),
                            'value_type': 'integer' if isinstance(value, int) else 'string',
                            'is_active': True
                        }
                    )
            
            return JsonResponse({'success': True, 'message': '设置保存成功'})
            
        except Exception as e:
            logger.error(f"保存系统设置失败: {str(e)}")
            return JsonResponse({'success': False, 'message': '保存失败'})