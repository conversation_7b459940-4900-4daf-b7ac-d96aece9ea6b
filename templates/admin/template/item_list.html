{% extends "admin/base_admin.html" %}

{% block page_title %}评分项管理{% endblock %}
{% block page_description %}{{ template.name }} - 管理模板的评分项目{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:template_detail' template.pk %}" 
       class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回模板详情
    </a>
    <a href="{% url 'evaluations:admin:item_create' template.pk %}" 
       class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
        <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>添加评分项
    </a>
</div>
{% endblock %}

{% block admin_content %}
{% csrf_token %}
<!-- 模板信息 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">模板信息</h3>
    </div>
    <div class="px-6 py-4">
        <div class="grid grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">模板名称</label>
                <p class="mt-1 text-sm text-gray-900">{{ template.name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">模板类型</label>
                <p class="mt-1 text-sm text-gray-900">{{ template.get_template_type_display }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">评分项数量</label>
                <p class="mt-1 text-sm text-gray-900">{{ items|length }} 个</p>
            </div>
        </div>
    </div>
</div>

<!-- 评分项列表 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">评分项列表</h3>
            <div class="flex items-center space-x-4">
                <!-- 批量操作工具栏 -->
                <div id="batchToolbar" class="hidden items-center space-x-3 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                    <span class="text-sm text-blue-700">已选择 <span id="selectedCount">0</span> 项</span>
                    <button id="batchDeleteBtn" class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                        <i data-lucide="trash-2" class="w-4 h-4 mr-1 inline"></i>批量删除
                    </button>
                    <button id="cancelSelectionBtn" class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50">
                        取消选择
                    </button>
                </div>
                <div class="text-sm text-gray-500">
                    共 {{ items|length }} 个评分项
                </div>
            </div>
        </div>
    </div>
    
    {% if items %}
    <div class="overflow-x-auto">
        <table class="min-w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" title="全选/取消全选">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评分项名称</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评分方式</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分值范围</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权重</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">必填</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in items %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="item-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" data-item-id="{{ item.pk }}" data-item-name="{{ item.name }}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                            {{ item.sort_order|default:forloop.counter }}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <div>
                            <p class="font-medium text-gray-900">{{ item.name }}</p>
                            {% if item.description %}
                            <p class="text-sm text-gray-500 mt-1">{{ item.description|truncatechars:50 }}</p>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if item.scoring_mode == 'score' %}bg-blue-100 text-blue-800
                            {% elif item.scoring_mode == 'level' %}bg-green-100 text-green-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ item.get_scoring_mode_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {% if item.scoring_mode == 'score' %}
                            0 - {{ item.max_score }}
                        {% elif item.scoring_mode == 'level' %}
                            {{ item.scoringtier_set.count }} 个等级
                        {% else %}
                            文本评价
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ item.weight }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if item.is_required %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                必填
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                选填
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if item.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                启用
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                禁用
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <a href="{% url 'evaluations:admin:item_update' template.pk item.pk %}" 
                               class="text-blue-600 hover:text-blue-900">编辑</a>
                            <button onclick="deleteItem({{ item.pk }}, '{{ item.name }}')" 
                                    class="text-red-600 hover:text-red-900">删除</button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-12">
        <i data-lucide="list-x" class="w-16 h-16 text-gray-300 mx-auto mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无评分项</h3>
        <p class="text-gray-500 mb-4">该模板还没有配置任何评分项目。</p>
        <a href="{% url 'evaluations:admin:item_create' template.pk %}" 
           class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-flex items-center">
            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>添加评分项
        </a>
    </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 多选和批量操作功能
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const batchToolbar = document.getElementById('batchToolbar');
        const selectedCountSpan = document.getElementById('selectedCount');
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        const cancelSelectionBtn = document.getElementById('cancelSelectionBtn');

        // 全选/取消全选功能
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateBatchToolbar();
        });

        // 单个checkbox改变时更新状态
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectAllState();
                updateBatchToolbar();
            });
        });

        // 取消选择按钮
        cancelSelectionBtn.addEventListener('click', function() {
            selectAllCheckbox.checked = false;
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateBatchToolbar();
        });

        // 批量删除按钮
        batchDeleteBtn.addEventListener('click', function() {
            const selectedItems = getSelectedItems();
            if (selectedItems.length > 0) {
                batchDeleteItems(selectedItems);
            }
        });

        // 更新全选checkbox状态
        function updateSelectAllState() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            const totalCount = itemCheckboxes.length;
            
            if (checkedCount === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCount === totalCount) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }

        // 更新批量操作工具栏
        function updateBatchToolbar() {
            const selectedCount = document.querySelectorAll('.item-checkbox:checked').length;
            selectedCountSpan.textContent = selectedCount;
            
            if (selectedCount > 0) {
                batchToolbar.classList.remove('hidden');
                batchToolbar.classList.add('flex');
            } else {
                batchToolbar.classList.add('hidden');
                batchToolbar.classList.remove('flex');
            }
        }

        // 获取选中的评分项
        function getSelectedItems() {
            const selectedItems = [];
            document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
                selectedItems.push({
                    id: checkbox.dataset.itemId,
                    name: checkbox.dataset.itemName
                });
            });
            return selectedItems;
        }
    });

    // 删除单个评分项
    function deleteItem(itemId, itemName) {
        if (confirm(`确定要删除评分项"${itemName}"吗？此操作不可恢复。`)) {
            const deleteUrl = `/evaluations/admin/templates/{{ template.pk }}/items/${itemId}/delete/`;
            
            fetch(deleteUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('评分项删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    throw new Error('删除失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('删除失败，请重试', 'error');
            });
        }
    }

    // 批量删除评分项
    function batchDeleteItems(selectedItems) {
        const itemNames = selectedItems.map(item => item.name).join('", "');
        const confirmMessage = `确定要删除以下 ${selectedItems.length} 个评分项吗？\n\n"${itemNames}"\n\n此操作不可恢复。`;
        
        if (confirm(confirmMessage)) {
            const itemIds = selectedItems.map(item => item.id);
            const batchDeleteUrl = `/evaluations/admin/templates/{{ template.pk }}/items/batch-delete/`;
            
            fetch(batchDeleteUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ item_ids: itemIds })
            })
            .then(response => {
                if (response.ok) {
                    showNotification(`成功删除 ${selectedItems.length} 个评分项`, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    throw new Error('批量删除失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('批量删除失败，请重试', 'error');
            });
        }
    }

    // 获取CSRF Token
    function getCsrfToken() {
        const formToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (formToken && formToken.value) {
            return formToken.value;
        }
        
        const metaToken = document.querySelector('meta[name=csrf-token]');
        if (metaToken && metaToken.content) {
            return metaToken.content;
        }
        
        return getCookie('csrftoken');
    }

    // 获取Cookie值
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // 通知函数
    function showNotification(message, type = 'info') {
        // 尝试使用全局通知函数
        if (typeof window.showNotification === 'function') {
            window.showNotification(message, type);
            return;
        }
        
        // 备用：创建简单通知
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
        
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
</script>
{% endblock %}
