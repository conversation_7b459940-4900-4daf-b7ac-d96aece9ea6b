#!/usr/bin/env python
"""
测试 sub 过滤器是否正常工作
"""
import os
import sys
import django

# 设置 Django 环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.template import Template, Context
from common.templatetags.table_extras import sub

def test_sub_filter():
    """测试 sub 过滤器功能"""
    print("=== 测试 sub 过滤器 ===\n")
    
    # 测试基本数值相减
    test_cases = [
        (10, 3, 7),
        (100, 25, 75),
        (0, 0, 0),
        (5, 10, -5),  # 负数结果
        (True, False, 1),  # 布尔值转换
        (False, True, -1),  # 布尔值转换
    ]
    
    print("1. 直接函数测试:")
    for value, arg, expected in test_cases:
        result = sub(value, arg)
        status = "✅" if result == expected else "❌"
        print(f"   {status} {value} - {arg} = {result} (期望: {expected})")
    
    # 测试模板中的使用
    print("\n2. 模板中使用测试:")
    template_str = """
    {% load table_extras %}
    总数: {{ total }}
    已完成: {{ completed }}
    待完成: {{ total|sub:completed }}
    """
    
    template = Template(template_str)
    context = Context({
        'total': 100,
        'completed': 35
    })
    
    try:
        result = template.render(context)
        print("   ✅ 模板渲染成功:")
        print(f"   {result.strip()}")
    except Exception as e:
        print(f"   ❌ 模板渲染失败: {e}")
    
    # 测试错误处理
    print("\n3. 错误处理测试:")
    error_cases = [
        ("abc", 5),
        (None, 10),
        (10, "xyz"),
    ]
    
    for value, arg in error_cases:
        try:
            result = sub(value, arg)
            print(f"   ✅ 错误处理正常: {value} - {arg} = {result}")
        except Exception as e:
            print(f"   ❌ 错误处理失败: {e}")

def test_batch_template():
    """测试批次详情模板是否能正常加载"""
    print("\n=== 测试批次详情模板 ===\n")
    
    from django.template.loader import get_template
    from django.template import Context
    
    try:
        # 尝试加载模板
        template = get_template('admin/batch/detail.html')
        print("✅ 模板加载成功")
        
        # 创建模拟数据
        class MockBatch:
            def __init__(self):
                self.name = "测试批次"
                self.get_relations_count = 100
                self.get_completed_count = 35
        
        context = Context({
            'batch': MockBatch(),
        })
        
        # 尝试渲染关键部分（只渲染包含 sub 过滤器的部分）
        template_str = """
        {% load table_extras %}
        {{ batch.get_relations_count|sub:batch.get_completed_count|default:0 }}
        """
        
        test_template = Template(template_str)
        result = test_template.render(context)
        
        print(f"✅ 待完成数量计算正确: {result.strip()}")
        
    except Exception as e:
        print(f"❌ 模板测试失败: {e}")

if __name__ == "__main__":
    test_sub_filter()
    test_batch_template()
    print("\n=== 测试完成 ===")
