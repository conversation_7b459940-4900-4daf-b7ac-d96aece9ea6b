{% extends "admin/base_admin.html" %}

{% block page_title %}匿名编号管理{% endblock %}
{% block page_description %}管理员工匿名编号的生成、迁移和安全设置{% endblock %}

{% block header_actions %}
<button id="migrateCodesBtn" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
    <i data-lucide="rotate-cw" class="w-4 h-4 mr-2 inline"></i>批量迁移
</button>
<button id="generateNewCodesBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
    <i data-lucide="shield" class="w-4 h-4 mr-2 inline"></i>生成新编号
</button>
{% endblock %}

{% block admin_content %}
<!-- Security Status Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">新安全编号</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.new_codes_count }}</p>
                <p class="text-xs text-green-600 mt-1">{{ stats.new_codes_percentage }}% 已升级</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
                <i data-lucide="alert-triangle" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">旧版编号</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.old_codes_count }}</p>
                <p class="text-xs text-yellow-600 mt-1">待迁移</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_staff }}</p>
                <p class="text-xs text-blue-600 mt-1">活跃员工</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
                <i data-lucide="calendar" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">最后更新</p>
                <p class="text-lg font-bold text-gray-900">{{ stats.last_migration|date:"m-d" }}</p>
                <p class="text-xs text-purple-600 mt-1">{{ stats.last_migration|date:"H:i" }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Security Upgrade Progress -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-gray-500"></i>
            安全升级进度
        </h3>
    </div>
    <div class="p-6">
        <div class="flex items-center justify-between mb-4">
            <span class="text-sm font-medium text-gray-700">升级完成度</span>
            <span class="text-sm font-medium text-gray-900">{{ stats.new_codes_percentage }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3 mb-6">
            <div class="bg-green-600 h-3 rounded-full transition-all duration-300" style="width: {{ stats.new_codes_percentage }}%"></div>
        </div>
        
        <div class="grid grid-cols-3 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ stats.new_codes_count }}</div>
                <div class="text-sm text-gray-500">已升级</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ stats.old_codes_count }}</div>
                <div class="text-sm text-gray-500">待升级</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ stats.total_staff }}</div>
                <div class="text-sm text-gray-500">总计</div>
            </div>
        </div>
    </div>
</div>

<!-- Anonymous Codes Management -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="key" class="w-5 h-5 mr-2 text-gray-500"></i>
                匿名编号列表
            </h3>
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <input type="text" id="codeSearch" placeholder="搜索员工或编号..." value="{{ search }}"
                        class="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                    <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <select id="codeStatusFilter" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有状态</option>
                    <option value="new" {% if status_filter == 'new' %}selected{% endif %}>新安全编号</option>
                    <option value="old" {% if status_filter == 'old' %}selected{% endif %}>旧版编号</option>
                </select>
                <button onclick="performServerSideFilter()" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    <i data-lucide="search" class="w-4 h-4 inline mr-1"></i>搜索
                </button>
                <button onclick="clearFilters()" class="px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                    <i data-lucide="x" class="w-4 h-4 inline mr-1"></i>清空
                </button>
            </div>
        </div>
    </div>
    <!-- 批量操作工具栏 -->
    <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-600">批量操作：</span>
                <button id="batchDeleteAnonymousBtn" onclick="showBatchDeleteAnonymousConfirm()" class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i data-lucide="trash-2" class="w-4 h-4 inline mr-1"></i>批量删除编号
                </button>
            </div>
            <span id="selectedAnonymousCount" class="text-sm text-gray-600">已选择 0 个员工</span>
        </div>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-700">
                            <input type="checkbox" id="selectAllCodes" class="checkbox">
                        </th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">员工信息</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">旧编号</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">新安全编号</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">状态</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">生成时间</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">操作</th>
                    </tr>
                </thead>
                <tbody id="codesTableBody">
                    {% for staff in staff_codes %}
                    <tr class="border-b border-gray-100 hover:bg-gray-50" data-staff-id="{{ staff.id }}">
                        <td class="py-4 px-4">
                            <input type="checkbox" class="code-checkbox checkbox" value="{{ staff.id }}">
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">{{ staff.name|first }}</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ staff.name }}</p>
                                    <p class="text-sm text-gray-500">{{ staff.employee_no }} · {{ staff.department.name }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="code-display text-sm bg-gray-100 px-3 py-1 rounded font-mono">
                                {{ staff.anonymous_code }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            {% if staff.new_anonymous_code %}
                                <span class="code-display text-sm bg-green-100 text-green-800 px-3 py-1 rounded font-mono">
                                    {{ staff.new_anonymous_code }}
                                </span>
                            {% else %}
                                <span class="text-sm text-gray-400 italic">未生成</span>
                            {% endif %}
                        </td>
                        <td class="py-4 px-4">
                            {% if staff.new_anonymous_code %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="shield-check" class="w-3 h-3 mr-1"></i>
                                    已升级
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>
                                    待升级
                                </span>
                            {% endif %}
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm text-gray-500">
                                {% if staff.anonymous_code_generated_at %}
                                    {{ staff.anonymous_code_generated_at|date:"Y-m-d H:i" }}
                                {% else %}
                                    -
                                {% endif %}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-2">
                                {% if not staff.new_anonymous_code %}
                                <button onclick="generateSingleCode({{ staff.id }})" 
                                    class="p-1 text-blue-600 hover:text-blue-800" title="生成新编号">
                                    <i data-lucide="shield-plus" class="w-4 h-4"></i>
                                </button>
                                {% else %}
                                <button onclick="regenerateCode({{ staff.id }})" 
                                    class="p-1 text-green-600 hover:text-green-800" title="重新生成">
                                    <i data-lucide="rotate-ccw" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteAnonymousCode({{ staff.id }}, '{{ staff.name }}')" 
                                    class="p-1 text-red-600 hover:text-red-800" title="删除匿名编号">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                                <button onclick="viewCodeHistory({{ staff.id }})" 
                                    class="p-1 text-gray-600 hover:text-gray-800" title="查看历史">
                                    <i data-lucide="history" class="w-4 h-4"></i>
                                </button>
                                <button onclick="testCodeLogin({{ staff.id }})" 
                                    class="p-1 text-purple-600 hover:text-purple-800" title="测试登录">
                                    <i data-lucide="external-link" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-700">
                显示第 {{ page_obj.start_index }}-{{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
            </div>
            <div class="flex items-center space-x-2">
                <!-- Previous Page -->
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                       class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                        <i data-lucide="chevron-left" class="w-4 h-4 inline mr-1"></i>上一页
                    </a>
                {% else %}
                    <span class="px-3 py-2 border border-gray-200 rounded-md text-sm text-gray-400 cursor-not-allowed">
                        <i data-lucide="chevron-left" class="w-4 h-4 inline mr-1"></i>上一页
                    </span>
                {% endif %}

                <!-- Page Numbers -->
                {% for num in paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                            {{ num }}
                        </a>
                    {% elif num == 1 %}
                        <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                            1
                        </a>
                        {% if page_obj.number > 4 %}
                            <span class="px-2 py-2 text-gray-500">...</span>
                        {% endif %}
                    {% elif num == paginator.num_pages %}
                        {% if page_obj.number < paginator.num_pages|add:'-3' %}
                            <span class="px-2 py-2 text-gray-500">...</span>
                        {% endif %}
                        <a href="?page={{ paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                            {{ paginator.num_pages }}
                        </a>
                    {% endif %}
                {% endfor %}

                <!-- Next Page -->
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                       class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                        下一页<i data-lucide="chevron-right" class="w-4 h-4 inline ml-1"></i>
                    </a>
                {% else %}
                    <span class="px-3 py-2 border border-gray-200 rounded-md text-sm text-gray-400 cursor-not-allowed">
                        下一页<i data-lucide="chevron-right" class="w-4 h-4 inline ml-1"></i>
                    </span>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="flex items-center justify-center mt-6">
            <div class="text-sm text-gray-500">
                共 {{ total_staff_codes }} 个员工（当前页显示全部）
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Security Settings -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="settings" class="w-5 h-5 mr-2 text-gray-500"></i>
            安全设置
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Encryption Settings -->
            <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">加密设置</h4>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">SHA256多层加密</p>
                            <p class="text-sm text-gray-500">三轮哈希算法确保编号安全性</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                            已启用
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">随机盐值</p>
                            <p class="text-sm text-gray-500">系统级盐值增强安全性</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                            已启用
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">格式验证</p>
                            <p class="text-sm text-gray-500">XXXX-XXXX-XXXX格式校验</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                            已启用
                        </span>
                    </div>
                </div>
            </div>

            <!-- Migration Settings -->
            <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">迁移设置</h4>
                <div class="space-y-4">
                    <div class="p-4 bg-blue-50 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <p class="font-medium text-gray-900">向后兼容</p>
                            <span class="text-sm text-blue-600">30天</span>
                        </div>
                        <p class="text-sm text-gray-600">支持新旧编号同时登录，平滑过渡</p>
                    </div>
                    
                    <div class="p-4 bg-yellow-50 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <p class="font-medium text-gray-900">批次处理</p>
                            <span class="text-sm text-yellow-600">100个/批</span>
                        </div>
                        <p class="text-sm text-gray-600">分批处理避免系统压力</p>
                    </div>
                    
                    <div class="p-4 bg-purple-50 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <p class="font-medium text-gray-900">版本控制</p>
                            <span class="text-sm text-purple-600">v2.0</span>
                        </div>
                        <p class="text-sm text-gray-600">记录编号算法版本便于追踪</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Batch Migration Modal -->
<div id="batchMigrationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">批量迁移匿名编号</h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">将为以下员工生成新的安全匿名编号：</p>
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-3">
                        <p class="text-sm text-yellow-800">
                            <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-1"></i>
                            共 <span id="migrationCount">{{ stats.old_codes_count }}</span> 个员工待迁移
                        </p>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="enableBackwardCompat" checked class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">保持30天向后兼容（推荐）</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="dryRunMode" class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">预演模式（不实际修改数据）</span>
                    </label>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBatchMigrationModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button onclick="startBatchMigration()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">开始迁移</button>
            </div>
        </div>
    </div>
</div>

<!-- Anonymous Code Delete Modal -->
<div id="anonymousDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-red-600"></i>
                    删除匿名编号确认
                </h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">您确定要删除以下员工的匿名编号吗？</p>
                    <div id="deleteAnonymousStaffInfo" class="bg-red-50 p-3 rounded border border-red-200 text-sm"></div>
                </div>
                <div id="deleteAnonymousConflicts" class="mb-4 hidden">
                    <p class="text-sm font-medium text-red-700 mb-2">检测到数据冲突：</p>
                    <div id="anonymousConflictsList" class="bg-red-50 p-3 rounded border border-red-200 text-sm"></div>
                    <label class="flex items-center mt-3">
                        <input type="checkbox" id="forceDeleteAnonymous" class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">我了解风险，强制删除</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label for="deleteAnonymousReason" class="block text-sm font-medium text-gray-700 mb-2">删除原因 <span class="text-red-500">*</span></label>
                    <textarea id="deleteAnonymousReason" rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                        placeholder="请输入删除原因..."></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeAnonymousDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button id="confirmAnonymousDeleteBtn" onclick="confirmAnonymousDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Anonymous Delete Modal -->
<div id="batchAnonymousDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-red-600"></i>
                    批量删除匿名编号确认
                </h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">您确定要删除以下 <span id="batchAnonymousDeleteCount">0</span> 个员工的匿名编号吗？</p>
                    <div id="batchAnonymousDeleteList" class="max-h-32 overflow-y-auto border border-gray-200 rounded p-2 text-sm bg-red-50"></div>
                </div>
                <div id="batchAnonymousDeleteConflicts" class="mb-4 hidden">
                    <p class="text-sm font-medium text-red-700 mb-2">检测到以下员工的匿名编号存在数据冲突：</p>
                    <div id="batchAnonymousConflictsList" class="max-h-32 overflow-y-auto bg-red-50 p-3 rounded border border-red-200 text-sm"></div>
                    <label class="flex items-center mt-3">
                        <input type="checkbox" id="batchForceDeleteAnonymous" class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">我了解风险，强制删除所有匿名编号</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label for="batchAnonymousDeleteReason" class="block text-sm font-medium text-gray-700 mb-2">删除原因 <span class="text-red-500">*</span></label>
                    <textarea id="batchAnonymousDeleteReason" rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                        placeholder="请输入批量删除原因..."></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBatchAnonymousDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button id="confirmBatchAnonymousDeleteBtn" onclick="confirmBatchAnonymousDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认批量删除</button>
            </div>
        </div>
    </div>
</div>

<!-- Code Generation Modal -->
<div id="codeGenerationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">生成新匿名编号</h3>
            </div>
            <div class="p-6">
                <div id="generationProgress" class="hidden">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="ml-3 text-gray-600">正在生成安全编号...</span>
                    </div>
                </div>
                <div id="generationResult" class="hidden">
                    <div class="text-center py-4">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="check" class="w-8 h-8 text-green-600"></i>
                        </div>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">生成成功！</h4>
                        <p class="text-sm text-gray-600 mb-4">新的安全匿名编号：</p>
                        <div class="bg-green-50 border border-green-200 rounded p-3">
                            <span id="newGeneratedCode" class="code-display text-lg font-mono text-green-800"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeCodeGenerationModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">完成</button>
            </div>
        </div>
    </div>
</div>

<style>
    .code-display {
        font-family: 'Courier New', monospace;
        letter-spacing: 1px;
    }
</style>

<script>
    // Search and filter functionality - 使用服务器端筛选
    document.getElementById('codeSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performServerSideFilter();
        }
    });
    
    document.getElementById('codeStatusFilter').addEventListener('change', performServerSideFilter);

    function performServerSideFilter() {
        const searchTerm = document.getElementById('codeSearch').value.trim();
        const statusFilter = document.getElementById('codeStatusFilter').value;
        
        // 构建URL参数
        const params = new URLSearchParams();
        if (searchTerm) {
            params.append('search', searchTerm);
        }
        if (statusFilter) {
            params.append('status', statusFilter);
        }
        
        // 重新加载页面并保持筛选条件
        const url = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
        window.location.href = url;
    }

    function clearFilters() {
        document.getElementById('codeSearch').value = '';
        document.getElementById('codeStatusFilter').value = '';
        window.location.href = window.location.pathname;
    }

    // 设置筛选器的初始值（从URL参数读取）
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const search = urlParams.get('search');
        const status = urlParams.get('status');
        
        if (search) {
            document.getElementById('codeSearch').value = search;
        }
        if (status) {
            document.getElementById('codeStatusFilter').value = status;
        }
    });

    // Batch migration functionality
    document.getElementById('migrateCodesBtn').addEventListener('click', function() {
        document.getElementById('batchMigrationModal').classList.remove('hidden');
    });

    function closeBatchMigrationModal() {
        document.getElementById('batchMigrationModal').classList.add('hidden');
    }

    function startBatchMigration() {
        const dryRun = document.getElementById('dryRunMode').checked;
        const backwardCompat = document.getElementById('enableBackwardCompat').checked;

        fetch('{% url "organizations:admin:migrate_anonymous_codes_batch" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                dry_run: dryRun,
                backward_compatible: backwardCompat
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`${dryRun ? '预演' : '迁移'}完成：${data.migrated_count} 个编号`, 'success');
                closeBatchMigrationModal();
                if (!dryRun) {
                    location.reload();
                }
            } else {
                showNotification(data.message || '迁移失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        });
    }

    // Code generation functionality
    document.getElementById('generateNewCodesBtn').addEventListener('click', function() {
        const selectedStaff = document.querySelectorAll('.code-checkbox:checked');
        if (selectedStaff.length === 0) {
            alert('请先选择要生成新编号的员工');
            return;
        }
        
        generateBatchCodes();
    });

    function generateBatchCodes() {
        const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');
        const staffIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
        
        if (staffIds.length === 0) {
            showNotification('请先选择要生成新编号的员工', 'error');
            return;
        }
        
        // 显示生成确认
        if (confirm(`确定要为选中的 ${staffIds.length} 个员工生成新的匿名编号吗？\n\n注意：如果员工已有新编号，将会覆盖原有编号。`)) {
            // 显示进度模态框
            document.getElementById('codeGenerationModal').classList.remove('hidden');
            document.getElementById('generationProgress').classList.remove('hidden');
            document.getElementById('generationResult').classList.add('hidden');
            
            // 发送批量生成请求
            fetch('{% url "organizations:admin:generate_batch_anonymous_codes" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    staff_ids: staffIds
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('generationProgress').classList.add('hidden');
                
                if (data.success) {
                    // 显示批量生成结果
                    displayBatchGenerationResult(data);
                } else {
                    closeCodeGenerationModal();
                    showNotification(data.message || '批量生成失败', 'error');
                }
            })
            .catch(error => {
                document.getElementById('generationProgress').classList.add('hidden');
                closeCodeGenerationModal();
                console.error('Error:', error);
                showNotification('网络错误，批量生成失败', 'error');
            });
        }
    }

    function displayBatchGenerationResult(data) {
        const resultDiv = document.getElementById('generationResult');
        const generatedCodeDiv = document.getElementById('newGeneratedCode');
        
        // 更新结果显示
        resultDiv.innerHTML = `
            <div class="text-center py-4">
                <div class="w-16 h-16 ${data.failed_count > 0 ? 'bg-yellow-100' : 'bg-green-100'} rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="${data.failed_count > 0 ? 'alert-triangle' : 'check'}" class="w-8 h-8 ${data.failed_count > 0 ? 'text-yellow-600' : 'text-green-600'}"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">批量生成完成！</h4>
                <p class="text-sm text-gray-600 mb-4">
                    成功生成：${data.success_count} 个<br>
                    失败：${data.failed_count} 个
                </p>
                ${data.failed_count > 0 ? `
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
                        <p class="text-sm text-yellow-800">部分员工生成失败，请检查详细信息</p>
                    </div>
                ` : ''}
                <div class="max-h-32 overflow-y-auto text-left">
                    ${data.results.map(result => `
                        <div class="flex items-center justify-between py-1 px-2 text-sm ${result.success ? 'text-green-700' : 'text-red-700'}">
                            <span>${result.staff_name}</span>
                            <span class="font-mono">
                                ${result.success ? result.new_code : '失败'}
                            </span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        resultDiv.classList.remove('hidden');
        
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    function generateSingleCode(staffId) {
        document.getElementById('codeGenerationModal').classList.remove('hidden');
        document.getElementById('generationProgress').classList.remove('hidden');
        document.getElementById('generationResult').classList.add('hidden');

        fetch('{% url "organizations:admin:generate_single_anonymous_code" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                staff_id: staffId
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('generationProgress').classList.add('hidden');
            if (data.success) {
                document.getElementById('newGeneratedCode').textContent = data.new_code;
                document.getElementById('generationResult').classList.remove('hidden');
            } else {
                closeCodeGenerationModal();
                showNotification(data.message || '生成失败', 'error');
            }
        })
        .catch(error => {
            document.getElementById('generationProgress').classList.add('hidden');
            closeCodeGenerationModal();
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        });
    }

    function regenerateCode(staffId) {
        if (confirm('确定要重新生成该员工的匿名编号吗？原编号将失效。')) {
            generateSingleCode(staffId);
        }
    }

    function closeCodeGenerationModal() {
        document.getElementById('codeGenerationModal').classList.add('hidden');
        location.reload(); // Refresh to show updated data
    }

    // Other utility functions
    function viewCodeHistory(staffId) {
        window.open('/admin/anonymous-codes/staff/' + staffId + '/history/', '_blank');
    }

    function testCodeLogin(staffId) {
        window.open('{% url "organizations:anonymous:anonymous_login" %}', '_blank');
    }

    // Select all functionality
    document.getElementById('selectAllCodes').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.code-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedAnonymousCount();
    });

    // 监听单个复选框变化
    document.addEventListener('change', function(event) {
        if (event.target.classList.contains('code-checkbox')) {
            updateSelectedAnonymousCount();
        }
    });

    // 更新选中数量和批量删除按钮状态
    function updateSelectedAnonymousCount() {
        const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        // 更新批量操作工具栏中的计数
        document.getElementById('selectedAnonymousCount').textContent = `已选择 ${count} 个员工`;
        
        // 启用/禁用批量删除按钮
        const batchDeleteBtn = document.getElementById('batchDeleteAnonymousBtn');
        if (count > 0) {
            batchDeleteBtn.disabled = false;
            batchDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            batchDeleteBtn.disabled = true;
            batchDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }

    // 删除功能相关变量
    let currentDeleteAnonymousStaffId = null;
    let selectedAnonymousStaffIds = [];

    // 单个匿名编号删除
    function deleteAnonymousCode(staffId, staffName) {
        currentDeleteAnonymousStaffId = staffId;
        
        // 获取员工详细信息
        const row = document.querySelector(`tr[data-staff-id="${staffId}"]`);
        if (!row) {
            console.error('找不到员工行数据，staffId:', staffId);
            showNotification('获取员工信息失败', 'error');
            return;
        }
        
        // 修复DOM选择器
        const staffNoElement = row.querySelector('td:nth-child(2) .text-gray-500');
        const staffNo = staffNoElement ? staffNoElement.textContent.split(' · ')[0] : '未知';
        const department = staffNoElement ? staffNoElement.textContent.split(' · ')[1] : '未知';
        const anonymousCodeElement = row.querySelector('td:nth-child(4) .bg-green-100');
        const anonymousCode = anonymousCodeElement ? anonymousCodeElement.textContent.trim() : '无';
        
        // 设置员工信息
        document.getElementById('deleteAnonymousStaffInfo').innerHTML = `
            <strong>${staffName}</strong> (${staffNo})<br>
            部门：${department}<br>
            当前匿名编号：${anonymousCode}
        `;
        
        // 清空之前的数据
        document.getElementById('deleteAnonymousConflicts').classList.add('hidden');
        document.getElementById('deleteAnonymousReason').value = '';
        document.getElementById('forceDeleteAnonymous').checked = false;
        
        // 显示删除确认模态框
        document.getElementById('anonymousDeleteModal').classList.remove('hidden');
    }

    // 关闭删除确认模态框
    function closeAnonymousDeleteModal() {
        document.getElementById('anonymousDeleteModal').classList.add('hidden');
        currentDeleteAnonymousStaffId = null;
    }

    // 确认删除单个匿名编号
    function confirmAnonymousDelete() {
        const reason = document.getElementById('deleteAnonymousReason').value.trim();
        const forceDelete = document.getElementById('forceDeleteAnonymous').checked;
        
        if (!reason) {
            showNotification('请输入删除原因', 'error');
            return;
        }
        
        // 禁用确认按钮防止重复提交
        const confirmBtn = document.getElementById('confirmAnonymousDeleteBtn');
        confirmBtn.disabled = true;
        confirmBtn.textContent = '删除中...';
        
        // 发送删除请求
        fetch('{% url "organizations:admin:anonymous_code_delete" 0 %}'.replace('0', currentDeleteAnonymousStaffId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                reason: reason,
                force_delete: forceDelete
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message || '删除成功', 'success');
                closeAnonymousDeleteModal();
                location.reload();
            } else if (data.has_conflicts) {
                // 显示数据冲突信息
                displayAnonymousDeleteConflicts(data.conflicts);
            } else {
                showNotification(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        })
        .finally(() => {
            confirmBtn.disabled = false;
            confirmBtn.textContent = '确认删除';
        });
    }

    // 显示删除冲突信息
    function displayAnonymousDeleteConflicts(conflicts) {
        const conflictsDiv = document.getElementById('deleteAnonymousConflicts');
        const conflictsList = document.getElementById('anonymousConflictsList');
        
        conflictsList.innerHTML = conflicts.map(conflict => `<div>• ${conflict}</div>`).join('');
        conflictsDiv.classList.remove('hidden');
    }

    // 批量删除确认
    function showBatchDeleteAnonymousConfirm() {
        const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');
        
        if (selectedCheckboxes.length === 0) {
            showNotification('请先选择要删除匿名编号的员工', 'error');
            return;
        }
        
        // 收集选中的有匿名编号的员工信息
        const selectedStaff = [];
        selectedAnonymousStaffIds = [];
        
        selectedCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const staffName = row.querySelector('td:nth-child(2) .font-medium').textContent;
            const staffNo = row.querySelector('td:nth-child(2) .text-gray-500').textContent.split(' · ')[0];
            const department = row.querySelector('td:nth-child(2) .text-gray-500').textContent.split(' · ')[1];
            const hasNewCode = row.querySelector('td:nth-child(4) .bg-green-100');
            
            // 只处理有新匿名编号的员工
            if (hasNewCode) {
                selectedStaff.push(`${staffName} (${staffNo}) - ${department}`);
                selectedAnonymousStaffIds.push(checkbox.value);
            }
        });
        
        if (selectedStaff.length === 0) {
            showNotification('所选员工中没有新匿名编号可以删除', 'error');
            return;
        }
        
        // 设置批量删除信息
        document.getElementById('batchAnonymousDeleteCount').textContent = selectedStaff.length;
        document.getElementById('batchAnonymousDeleteList').innerHTML = selectedStaff.join('<br>');
        
        // 清空之前的数据
        document.getElementById('batchAnonymousDeleteConflicts').classList.add('hidden');
        document.getElementById('batchAnonymousDeleteReason').value = '';
        document.getElementById('batchForceDeleteAnonymous').checked = false;
        
        // 显示批量删除确认模态框
        document.getElementById('batchAnonymousDeleteModal').classList.remove('hidden');
    }

    // 关闭批量删除模态框
    function closeBatchAnonymousDeleteModal() {
        document.getElementById('batchAnonymousDeleteModal').classList.add('hidden');
        selectedAnonymousStaffIds = [];
    }

    // 确认批量删除
    function confirmBatchAnonymousDelete() {
        const reason = document.getElementById('batchAnonymousDeleteReason').value.trim();
        const forceDelete = document.getElementById('batchForceDeleteAnonymous').checked;
        
        if (!reason) {
            showNotification('请输入删除原因', 'error');
            return;
        }
        
        // 禁用确认按钮防止重复提交
        const confirmBtn = document.getElementById('confirmBatchAnonymousDeleteBtn');
        confirmBtn.disabled = true;
        confirmBtn.textContent = '删除中...';
        
        // 发送批量删除请求
        fetch('{% url "organizations:admin:anonymous_code_batch_delete" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                staff_ids: selectedAnonymousStaffIds,
                reason: reason,
                force_delete: forceDelete
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`批量删除完成：成功 ${data.success_count} 个，失败 ${data.failed_count} 个`, 'success');
                closeBatchAnonymousDeleteModal();
                location.reload();
            } else if (data.has_conflicts) {
                // 显示批量删除冲突信息
                displayBatchAnonymousDeleteConflicts(data.conflict_items);
            } else {
                showNotification(data.message || '批量删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        })
        .finally(() => {
            confirmBtn.disabled = false;
            confirmBtn.textContent = '确认批量删除';
        });
    }

    // 显示批量删除冲突信息
    function displayBatchAnonymousDeleteConflicts(conflictItems) {
        const conflictsDiv = document.getElementById('batchAnonymousDeleteConflicts');
        const conflictsList = document.getElementById('batchAnonymousConflictsList');
        
        const conflictsHtml = conflictItems.map(item => `
            <div class="mb-2">
                <strong>${item.name} (${item.employee_no}) - ${item.department}</strong>
                <div class="ml-4 text-xs">
                    ${item.conflicts.map(conflict => `• ${conflict}`).join('<br>')}
                </div>
            </div>
        `).join('');
        
        conflictsList.innerHTML = conflictsHtml;
        conflictsDiv.classList.remove('hidden');
    }

    // 通知函数
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.style.maxWidth = '400px';
        notification.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message.replace(/\n/g, '<br>')}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动消失
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
        
        // 初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Utility functions
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}