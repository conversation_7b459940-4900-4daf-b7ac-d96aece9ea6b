{% extends "anonymous/base_anonymous.html" %}

{% block page_title %}考评打分{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .evaluation-card {
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        background: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        transform: translateY(0);
    }

    .evaluation-card:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
    }

    .evaluation-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .card-decoration {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 4rem;
        height: 4rem;
        background: #f1f5f9;
        border-radius: 50%;
        opacity: 0.6;
    }

    .card-decoration-small {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        width: 2rem;
        height: 2rem;
        background: #ecfdf5;
        border-radius: 50%;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen p-6 bg-gray-50">
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- 页面标题 -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">考评打分</h1>
                <p class="text-gray-600 mt-2">请根据该员工的实际工作表现客观公正地进行评分</p>
            </div>
        </div>

        <!-- 评价对象信息卡片 -->
        <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
            <!-- 顶部状态指示条 -->
            <div class="absolute top-0 left-0 right-0 h-1 bg-emerald-500"></div>

            <!-- 主卡片内容 -->
            <div class="relative p-6 h-full">
                <!-- 右上角装饰 -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                <div class="relative z-10">
                    <!-- 头部区域 -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <!-- 评价对象图标 -->
                            <div class="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                <i data-lucide="user" class="w-6 h-6 text-white"></i>
                            </div>

                            <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                                评价对象：{{ evaluatee.name }}
                            </h3>

                            <!-- 基本信息标签 -->
                            <div class="flex items-center gap-2 mb-4">
                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-emerald-50 text-emerald-700 border border-emerald-200">
                                    {{ evaluatee.department.name }}
                                </span>
                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700 border border-slate-200">
                                    {{ evaluatee.position.name }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- 考评批次信息卡片 -->
                    <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                                </div>
                                <div class="text-lg font-bold text-slate-900">{{ relation.batch.name }}</div>
                                <div class="text-xs text-slate-500">考评批次</div>
                            </div>
                            <div class="text-center">
                                <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="clock" class="w-4 h-4 text-white"></i>
                                </div>
                                <div class="text-sm font-semibold text-slate-900">{{ relation.batch.start_date|date:"m-d" }} ~ {{ relation.batch.end_date|date:"m-d" }}</div>
                                <div class="text-xs text-slate-500">评分期间</div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部装饰线 -->
                    <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                </div>

                <!-- 悬停时的微妙阴影效果 -->
                <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
        </div>

        <!-- 考评说明卡片 -->
        <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200">
            <!-- 顶部状态指示条 -->
            <div class="absolute top-0 left-0 right-0 h-1 bg-blue-500"></div>

            <!-- 主卡片内容 -->
            <div class="relative p-6 h-full">
                <!-- 右上角装饰 -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                <div class="absolute top-6 right-6 w-8 h-8 bg-blue-50 rounded-full"></div>

                <div class="relative z-10">
                    <!-- 头部区域 -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <!-- 说明图标 -->
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                <i data-lucide="info" class="w-6 h-6 text-white"></i>
                            </div>

                            <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                考评说明
                            </h3>

                            <!-- 说明内容 -->
                            <div class="text-sm text-slate-600 space-y-2 leading-relaxed">
                                <p class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    请根据该员工的实际工作表现客观公正地进行评分
                                </p>
                                <p class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    您的评分将完全匿名，被评价者无法知晓评分来源
                                </p>
                                <p class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    评分完成后请务必点击"提交评分"按钮保存
                                </p>
                                <p class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    如有疑问，请联系HR部门或系统管理员
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 底部装饰线 -->
                    <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                </div>

                <!-- 悬停时的微妙阴影效果 -->
                <div class="absolute inset-0 bg-blue-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
        </div>

        <!-- 评分表单 -->
        <form id="evaluationForm" method="post" action="{% url 'evaluations:anonymous:submit' relation.id %}">
            {% csrf_token %}

            <!-- 模板信息卡片 -->
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200 mb-6">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-purple-500"></div>

                <!-- 主卡片内容 -->
                <div class="relative p-6 h-full">
                    <!-- 右上角装饰 -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                    <div class="absolute top-6 right-6 w-8 h-8 bg-purple-50 rounded-full"></div>

                    <div class="relative z-10">
                        <!-- 头部区域 -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <!-- 模板图标 -->
                                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                                </div>

                                <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-purple-600 transition-colors duration-300">
                                    {{ template.name }}
                                </h3>

                                <!-- 模板描述 -->
                                {% if template.description %}
                                    <p class="text-slate-600 text-sm leading-relaxed mb-4 line-clamp-2">{{ template.description }}</p>
                                {% endif %}

                                <!-- 评分模式标签 -->
                                <div class="flex items-center gap-2 mb-4">
                                    <span class="px-3 py-1 rounded-full text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200">
                                        {% if template.template_type == 'structured' %}结构化评分
                                        {% elif template.template_type == 'open' %}开放式评分
                                        {% elif template.template_type == 'mixed' %}混合式评分
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

            <!-- 评分项列表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8" id="evaluationItems">
                {% for item in evaluation_items %}
                    <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200 evaluation-item" data-item-id="{{ item.id }}">
                        <!-- 顶部状态指示条 -->
                        <div class="absolute top-0 left-0 right-0 h-1
                            {% if item.scoring_mode == 'numeric' %}bg-blue-500
                            {% elif item.scoring_mode == 'tier' %}bg-green-500
                            {% elif item.scoring_mode == 'text' %}bg-purple-500
                            {% endif %}"></div>

                        <!-- 主卡片内容 -->
                        <div class="relative p-6 h-full">
                            <!-- 右上角装饰 -->
                            <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                            <div class="absolute top-6 right-6 w-8 h-8
                                {% if item.scoring_mode == 'numeric' %}bg-blue-50
                                {% elif item.scoring_mode == 'tier' %}bg-green-50
                                {% elif item.scoring_mode == 'text' %}bg-purple-50
                                {% endif %} rounded-full"></div>

                            <div class="relative z-10">
                                <!-- 头部区域 -->
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <!-- 评分项图标 -->
                                        <div class="w-12 h-12
                                            {% if item.scoring_mode == 'numeric' %}bg-blue-600
                                            {% elif item.scoring_mode == 'tier' %}bg-green-600
                                            {% elif item.scoring_mode == 'text' %}bg-purple-600
                                            {% endif %} rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                            <i data-lucide="
                                                {% if item.scoring_mode == 'numeric' %}calculator
                                                {% elif item.scoring_mode == 'tier' %}star
                                                {% elif item.scoring_mode == 'text' %}message-circle
                                                {% endif %}" class="w-6 h-6 text-white"></i>
                                        </div>

                                        <h4 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-
                                            {% if item.scoring_mode == 'numeric' %}blue-600
                                            {% elif item.scoring_mode == 'tier' %}green-600
                                            {% elif item.scoring_mode == 'text' %}purple-600
                                            {% endif %} transition-colors duration-300">
                                            {{ item.name }}
                                            {% if item.is_required %}
                                                <span class="text-red-500 ml-1">*</span>
                                            {% endif %}
                                        </h4>

                                        <!-- 评分模式和描述 -->
                                        <div class="flex items-center gap-2 mb-4">
                                            <span class="px-3 py-1 rounded-full text-xs font-medium
                                                {% if item.scoring_mode == 'numeric' %}bg-blue-50 text-blue-700 border border-blue-200
                                                {% elif item.scoring_mode == 'tier' %}bg-green-50 text-green-700 border border-green-200
                                                {% elif item.scoring_mode == 'text' %}bg-purple-50 text-purple-700 border border-purple-200
                                                {% endif %}">
                                                {% if item.scoring_mode == 'numeric' %}数值评分
                                                {% elif item.scoring_mode == 'tier' %}等级评分
                                                {% elif item.scoring_mode == 'text' %}文本评价
                                                {% endif %}
                                            </span>
                                            {% if item.is_required %}
                                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200">
                                                    必填项
                                                </span>
                                            {% endif %}
                                        </div>

                                        <!-- 描述区域 -->
                                        {% if item.description %}
                                            <p class="text-slate-600 text-sm leading-relaxed mb-6 line-clamp-2">{{ item.description }}</p>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- 评分内容区域 -->
                                <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                                    {% if item.scoring_mode == 'numeric' %}
                                        <!-- 数值评分 -->
                                        <div class="numeric-scoring" data-max-score="{{ item.max_score }}">
                                            <div class="text-center mb-4">
                                                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                                    <i data-lucide="calculator" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="text-sm text-slate-500 mb-2">当前评分</div>
                                                <div class="flex items-center justify-center space-x-2 mb-4">
                                                    <input type="number" name="items[{{ item.id }}][score]"
                                                           min="0" max="{{ item.max_score }}" step="0.1"
                                                           class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-center text-lg font-bold"
                                                           placeholder="0">
                                                    <span class="text-sm text-gray-500">/ {{ item.max_score }} 分</span>
                                                </div>
                                            </div>

                                            <!-- 评分滑块 -->
                                            <div class="mb-4">
                                                <input type="range" min="0" max="{{ item.max_score }}" step="0.1" value="0"
                                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                                       data-item-id="{{ item.id }}">
                                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                                    <span>0</span>
                                                    <span>{{ item.max_score|floatformat:0 }}</span>
                                                </div>
                                            </div>
                                        </div>

                                    {% elif item.scoring_mode == 'tier' %}
                                        <!-- 等级评分 -->
                                        <div class="tier-scoring">
                                            <div class="text-center mb-4">
                                                <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                                    <i data-lucide="star" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="text-sm text-slate-500">选择等级</div>
                                            </div>
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                                {% for tier in item.scoringtier_set.all %}
                                                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50 focus:outline-none transition-all duration-200">
                                                        <input type="radio" name="items[{{ item.id }}][tier]" value="{{ tier.id }}"
                                                               class="sr-only peer" data-score="{{ tier.tier_value }}">
                                                        <div class="flex flex-1 flex-col">
                                                            <div class="flex items-center justify-between">
                                                                <span class="block text-sm font-medium text-gray-900">{{ tier.tier_name }}</span>
                                                                <span class="ml-2 text-sm font-semibold text-green-600">{{ tier.tier_value }}分</span>
                                                            </div>
                                                            {% if tier.description %}
                                                                <span class="mt-1 block text-xs text-gray-500">{{ tier.description }}</span>
                                                            {% endif %}
                                                        </div>
                                                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-green-500"></div>
                                                    </label>
                                                {% endfor %}
                                            </div>
                                            <input type="hidden" name="items[{{ item.id }}][score]" value="0">
                                        </div>

                                    {% elif item.scoring_mode == 'text' %}
                                        <!-- 文本评价 -->
                                        <div class="text-scoring">
                                            <div class="text-center mb-4">
                                                <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                                    <i data-lucide="message-circle" class="w-4 h-4 text-white"></i>
                                                </div>
                                                <div class="text-sm text-slate-500">文字评价</div>
                                            </div>
                                            <textarea name="items[{{ item.id }}][text]" rows="4"
                                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                                                      placeholder="请输入您的评价和建议..."></textarea>
                                            <input type="hidden" name="items[{{ item.id }}][score]" value="0">
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- 底部装饰线 -->
                                <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                            </div>

                            <!-- 悬停时的微妙阴影效果 -->
                            <div class="absolute inset-0 bg-
                                {% if item.scoring_mode == 'numeric' %}blue-500/5
                                {% elif item.scoring_mode == 'tier' %}green-500/5
                                {% elif item.scoring_mode == 'text' %}purple-500/5
                                {% endif %} rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- 总体评价卡片 -->
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200 mt-6">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-orange-500"></div>

                <!-- 主卡片内容 -->
                <div class="relative p-6 h-full">
                    <!-- 右上角装饰 -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                    <div class="absolute top-6 right-6 w-8 h-8 bg-orange-50 rounded-full"></div>

                    <div class="relative z-10">
                        <!-- 头部区域 -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <!-- 总体评价图标 -->
                                <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                    <i data-lucide="message-circle" class="w-6 h-6 text-white"></i>
                                </div>

                                <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                                    总体评价
                                </h3>

                                <!-- 说明文字 -->
                                <p class="text-slate-600 text-sm leading-relaxed mb-6">请对该员工的整体表现进行综合评价（可选）</p>
                            </div>
                        </div>

                        <!-- 评价输入区域 -->
                        <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                            <div class="text-center mb-4">
                                <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="edit" class="w-4 h-4 text-white"></i>
                                </div>
                                <div class="text-sm text-slate-500">综合评价</div>
                            </div>
                            <textarea name="comment" rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                                      placeholder="请输入您的整体评价、建议或意见..."></textarea>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-orange-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

            <!-- 评分汇总卡片 -->
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200 mt-6" id="scoreSummary">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-indigo-500"></div>

                <!-- 主卡片内容 -->
                <div class="relative p-6 h-full">
                    <!-- 右上角装饰 -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                    <div class="absolute top-6 right-6 w-8 h-8 bg-indigo-50 rounded-full"></div>

                    <div class="relative z-10">
                        <!-- 头部区域 -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <!-- 汇总图标 -->
                                <div class="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mb-4 shadow-sm">
                                    <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                                </div>

                                <h3 class="text-xl font-bold text-slate-900 mb-3 group-hover:text-indigo-600 transition-colors duration-300">
                                    评分汇总
                                </h3>
                            </div>
                        </div>

                        <!-- 统计信息卡片 -->
                        <div class="bg-slate-50 rounded-lg p-4 mb-6 border border-slate-100">
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-center">
                                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-slate-900" id="currentScore">0</div>
                                    <div class="text-xs text-slate-500">当前总分</div>
                                </div>
                                <div class="text-center">
                                    <div class="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="award" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-slate-900" id="maxScore">{{ template.calculate_total_score }}</div>
                                    <div class="text-xs text-slate-500">满分</div>
                                </div>
                                <div class="text-center">
                                    <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i data-lucide="percent" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-slate-900" id="completionRate">0%</div>
                                    <div class="text-xs text-slate-500">完成度</div>
                                </div>
                            </div>
                        </div>

                        <!-- 进度条区域 -->
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-slate-700">评分进度</span>
                                <span class="text-sm text-slate-600" id="progressText">0 / {{ evaluation_items|length }} 项</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-indigo-600 h-3 rounded-full transition-all duration-300"
                                     style="width: 0%" id="progressBar"></div>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-indigo-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

            <!-- 提交按钮卡片 -->
            <div class="group relative overflow-hidden rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-slate-200 mt-6">
                <!-- 顶部状态指示条 -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-emerald-500"></div>

                <!-- 主卡片内容 -->
                <div class="relative p-6 h-full">
                    <!-- 右上角装饰 -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-slate-50 rounded-full opacity-60"></div>
                    <div class="absolute top-6 right-6 w-8 h-8 bg-emerald-50 rounded-full"></div>

                    <div class="relative z-10">
                        <!-- 提交区域 -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-slate-600">
                                <div class="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="shield-check" class="w-4 h-4 text-white"></i>
                                </div>
                                <span>您的评分将完全匿名提交，无法追溯评分来源</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button type="button" id="saveDraftBtn"
                                        class="flex-1 bg-slate-50 border border-slate-200 text-slate-700 hover:bg-slate-100 hover:border-slate-300 transition-all duration-300 px-4 py-2 rounded-lg text-sm flex items-center justify-center gap-2">
                                    <i data-lucide="save" class="w-4 h-4"></i>
                                    保存草稿
                                </button>
                                <button type="submit" id="submitBtn"
                                        class="bg-emerald-50 border border-emerald-200 text-emerald-700 hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-300 px-6 py-2 rounded-lg text-sm flex items-center justify-center gap-2">
                                    <i data-lucide="send" class="w-4 h-4"></i>
                                    提交评分
                                </button>
                            </div>
                        </div>

                        <!-- 底部装饰线 -->
                        <div class="absolute bottom-0 left-6 right-6 h-px bg-slate-100"></div>
                    </div>

                    <!-- 悬停时的微妙阴影效果 -->
                    <div class="absolute inset-0 bg-emerald-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let maxTotalScore = {{ template.calculate_total_score }};
    let totalItems = {{ evaluation_items|length }};

    // 初始化事件监听
    document.addEventListener('DOMContentLoaded', function() {
        initializeEventListeners();
        updateScoreSummary();
        
        // 加载草稿数据（如果存在）
        loadDraftData();
    });

    function initializeEventListeners() {
        // 数值评分输入框
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', function() {
                updateSlider(this);
                updateScoreSummary();
            });
        });

        // 滑块
        document.querySelectorAll('.slider').forEach(slider => {
            slider.addEventListener('input', function() {
                updateNumberInput(this);
                updateScoreSummary();
            });
        });

        // 等级评分单选框
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                updateTierScore(this);
                updateScoreSummary();
            });
        });

        // 文本评价
        document.querySelectorAll('textarea[name*="[text]"]').forEach(textarea => {
            textarea.addEventListener('input', function() {
                updateScoreSummary();
            });
        });
    }

    function updateSlider(numberInput) {
        const itemId = numberInput.name.match(/\[(\d+)\]/)[1];
        const slider = document.querySelector(`.slider[data-item-id="${itemId}"]`);
        if (slider) {
            slider.value = numberInput.value;
        }
    }

    function updateNumberInput(slider) {
        const itemId = slider.dataset.itemId;
        const numberInput = document.querySelector(`input[name="items[${itemId}][score]"]`);
        if (numberInput) {
            numberInput.value = slider.value;
        }
    }

    function updateTierScore(radio) {
        const score = radio.dataset.score;
        const itemId = radio.name.match(/\[(\d+)\]/)[1];
        const hiddenInput = document.querySelector(`input[name="items[${itemId}][score]"]`);
        if (hiddenInput) {
            hiddenInput.value = score;
        }
    }

    function updateScoreSummary() {
        let currentScore = 0;
        let completedItems = 0;

        // 计算当前总分和完成项数
        document.querySelectorAll('.evaluation-item').forEach(item => {
            const itemId = item.dataset.itemId;
            const scoreInput = item.querySelector(`input[name="items[${itemId}][score]"]`);
            const textInput = item.querySelector(`textarea[name="items[${itemId}][text]"]`);
            
            if (scoreInput) {
                const score = parseFloat(scoreInput.value) || 0;
                currentScore += score;
                
                if (score > 0 || (textInput && textInput.value.trim())) {
                    completedItems++;
                }
            } else if (textInput && textInput.value.trim()) {
                completedItems++;
            }
        });

        // 更新显示
        document.getElementById('currentScore').textContent = currentScore.toFixed(1);
        document.getElementById('completionRate').textContent = 
            maxTotalScore > 0 ? Math.round((currentScore / maxTotalScore) * 100) + '%' : '0%';
        
        document.getElementById('progressText').textContent = `${completedItems} / ${totalItems} 项`;
        
        const progressPercent = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;
        document.getElementById('progressBar').style.width = progressPercent + '%';
    }

    // 保存草稿
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('evaluationForm'));
        formData.append('save_draft', 'true');
        
        fetch('{% url "evaluations:anonymous:submit" relation.id %}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('草稿已保存', 'success');
                localStorage.setItem('draft_{{ relation.id }}', JSON.stringify(getFormData()));
            } else {
                showNotification('保存失败：' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('保存失败，请重试', 'error');
        });
    });

    // 表单提交
    document.getElementById('evaluationForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return;
        }
        
        // 显示确认对话框
        if (!confirm('确定要提交评分吗？提交后将无法修改。')) {
            e.preventDefault();
            return;
        }
        
        // 显示加载状态
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>提交中...';
        
        // 清除草稿
        localStorage.removeItem('draft_{{ relation.id }}');
    });

    function validateForm() {
        let isValid = true;
        let errors = [];

        // 检查必填项
        document.querySelectorAll('.evaluation-item').forEach(item => {
            const itemId = item.dataset.itemId;
            const itemName = item.querySelector('h4').textContent.trim();
            const isRequired = item.querySelector('span.text-red-500');
            
            if (isRequired) {
                const scoreInput = item.querySelector(`input[name="items[${itemId}][score]"]`);
                const textInput = item.querySelector(`textarea[name="items[${itemId}][text]"]`);
                
                let hasValue = false;
                if (scoreInput && parseFloat(scoreInput.value) > 0) {
                    hasValue = true;
                }
                if (textInput && textInput.value.trim()) {
                    hasValue = true;
                }
                
                if (!hasValue) {
                    errors.push(`"${itemName}" 为必填项，请完成评分`);
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            alert('请完成以下必填项：\n\n' + errors.join('\n'));
        }

        return isValid;
    }

    function getFormData() {
        const data = {};
        const formData = new FormData(document.getElementById('evaluationForm'));
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }

    function loadDraftData() {
        // 从服务器加载草稿数据
        fetch(`{% url 'evaluations:anonymous:draft' relation.id %}`)
            .then(response => response.json())
            .then(result => {
                if (result.success && result.data) {
                    const data = result.data;
                    
                    // 恢复总体评价
                    if (data.comment) {
                        document.querySelector('textarea[name="comment"]').value = data.comment;
                    }
                    
                    // 恢复评分项数据
                    for (let [itemId, itemData] of Object.entries(data.items)) {
                        // 恢复数值评分
                        if (itemData.score) {
                            const scoreInput = document.querySelector(`input[name="items[${itemId}][score]"]`);
                            const slider = document.querySelector(`.slider[data-item-id="${itemId}"]`);
                            if (scoreInput) {
                                scoreInput.value = itemData.score;
                                if (slider) {
                                    slider.value = itemData.score;
                                }
                            }
                        }
                        
                        // 恢复等级评分
                        if (itemData.tier) {
                            const radio = document.querySelector(`input[name="items[${itemId}][tier]"][value="${itemData.tier}"]`);
                            if (radio) {
                                radio.checked = true;
                                updateTierScore(radio);
                            }
                        }
                        
                        // 恢复文本评价
                        if (itemData.text) {
                            const textInput = document.querySelector(`textarea[name="items[${itemId}][text]"]`);
                            if (textInput) {
                                textInput.value = itemData.text;
                            }
                        }
                    }
                    
                    updateScoreSummary();
                    showNotification('已恢复草稿数据', 'info');
                    
                } else if (result.message) {
                    // 没有草稿数据，这是正常的
                    console.log('无草稿数据');
                }
            })
            .catch(error => {
                console.error('加载草稿数据失败:', error);
                // 如果服务器草稿加载失败，尝试从localStorage加载作为备用
                loadLocalDraftData();
            });
    }
    
    function loadLocalDraftData() {
        // 备用的本地草稿加载方法
        const draftKey = 'draft_{{ relation.id }}';
        const draftData = localStorage.getItem(draftKey);
        
        if (draftData) {
            try {
                const data = JSON.parse(draftData);
                
                // 恢复表单数据
                for (let [key, value] of Object.entries(data)) {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) {
                        if (element.type === 'radio') {
                            const radio = document.querySelector(`[name="${key}"][value="${value}"]`);
                            if (radio) {
                                radio.checked = true;
                                updateTierScore(radio);
                            }
                        } else {
                            element.value = value;
                        }
                    }
                }
                
                updateScoreSummary();
                showNotification('已恢复本地草稿数据', 'info');
                
            } catch (e) {
                console.error('加载本地草稿数据失败:', e);
            }
        }
    }

    // 页面离开前提醒
    window.addEventListener('beforeunload', function(e) {
        const formData = getFormData();
        const hasData = Object.values(formData).some(value => value && value.toString().trim());
        
        if (hasData) {
            e.preventDefault();
            e.returnValue = '您有未保存的评分数据，确定要离开吗？';
        }
    });

    // 定时自动保存草稿
    setInterval(function() {
        const formData = getFormData();
        const hasData = Object.values(formData).some(value => value && value.toString().trim());
        
        if (hasData) {
            localStorage.setItem('draft_{{ relation.id }}', JSON.stringify(formData));
        }
    }, 30000); // 每30秒自动保存一次
</script>
{% endblock %}